# Snapback License Management API - Swift macOS Integration Guide

## Table of Contents

1. [Overview](#overview)
2. [Environment Setup](#environment-setup)
3. [Authentication & Device Registration](#authentication--device-registration)
4. [API Endpoints](#api-endpoints)
5. [Swift Integration Examples](#swift-integration-examples)
6. [Error Handling](#error-handling)
7. [Rate Limiting & Security](#rate-limiting--security)
8. [Payment Integration](#payment-integration)
9. [Troubleshooting](#troubleshooting)

## Overview

The Snapback License Management API provides a complete solution for managing software licenses, device registration, and payment processing. This guide focuses on integrating the API with Swift macOS applications.

### License Types

- **Trial**: Free, 1 device, 14 days
- **Standard**: $4.99, 2 devices, lifetime
- **Extended**: $9.99, 5 devices, lifetime

### License Key Format

License keys are 24-character alphanumeric strings using a secure character set that excludes confusing characters (0, O, 1, I, L):

- **Character Set**: `ABCDEFGHJKMNPQRSTUVWXYZ23456789` (30 characters)
- **Length**: 24 characters
- **Display Format**: `XXXX-XXXX-XXXX-XXXX-XXXX-XXXX` (with dashes for readability)
- **API Format**: `XXXXXXXXXXXXXXXXXXXXXXXX` (normalized, no dashes)

**Example:**
- Display: `ABCD-EFGH-JKMN-PQRS-TUVW-XYZ2`
- API: `ABCDEFGHJKMNPQRSTUVWXYZ2`

**Important:** When sending license keys to the API, always normalize them by removing dashes. When displaying to users, format with dashes every 4 characters for better readability.

### Base URL

```
Production: https://your-domain.com/api
Development: http://localhost:3000/api
```

## Environment Setup

### Required Environment Variables

```bash
# Server Configuration
NODE_ENV=production
PORT=3000
DATABASE_URL=postgresql://user:password@localhost:5432/snapback

# CORS Configuration
CORS_ORIGIN=https://your-app-domain.com

# JWT Configuration
JWT_SECRET=your-super-secure-jwt-secret-at-least-32-characters-long

# Stripe Configuration
STRIPE_SECRET_KEY=sk_live_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>

# Security Configuration (Optional)
BLOCKED_IPS=*************,*********
REQUEST_SIGNING_SECRET=your-request-signing-secret

# Rate Limiting Configuration (Optional)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging Configuration (Optional)
LOG_LEVEL=info
```

## Authentication & Device Registration

### Device ID Generation

Generate a unique device identifier in your Swift app:

```swift
import Foundation
import CryptoKit

func generateDeviceID() -> String {
    let systemInfo = [
        ProcessInfo.processInfo.hostName,
        ProcessInfo.processInfo.operatingSystemVersionString,
        // Add more system-specific identifiers as needed
    ].joined(separator: "-")

    let data = Data(systemInfo.utf8)
    let hash = SHA256.hash(data: data)
    return hash.compactMap { String(format: "%02x", $0) }.joined()
}

// License key formatting utilities
func formatLicenseKeyForDisplay(_ licenseKey: String) -> String {
    let normalized = licenseKey.replacingOccurrences(of: "-", with: "")
    var formatted = ""
    for (index, character) in normalized.enumerated() {
        if index > 0 && index % 4 == 0 {
            formatted += "-"
        }
        formatted += String(character)
    }
    return formatted
}

func normalizeLicenseKeyForAPI(_ licenseKey: String) -> String {
    return licenseKey.replacingOccurrences(of: "-", with: "").uppercased()
}
```

### JWT Token Management

After successful license validation, store and manage the device token:

```swift
class TokenManager {
    private let keychain = Keychain(service: "com.yourapp.snapback")

    func storeDeviceToken(_ token: String) {
        keychain["device_token"] = token
    }

    func getDeviceToken() -> String? {
        return keychain["device_token"]
    }

    func clearDeviceToken() {
        keychain["device_token"] = nil
    }
}
```

## API Endpoints

### Payment Endpoints

#### 1. Get Pricing Information

**Endpoint:** `GET /payments/pricing`

**Description:** Get current pricing for all license types

**Response (200 OK):**

```json
{
  "trial": {
    "price": 0,
    "maxDevices": 1,
    "duration": "14 days"
  },
  "standard": {
    "price": 499,
    "maxDevices": 2,
    "duration": "Lifetime"
  },
  "extended": {
    "price": 999,
    "maxDevices": 5,
    "duration": "Lifetime"
  },
  "additionalDevice": {
    "price": 99,
    "description": "Per additional device"
  }
}
```

#### 2. Create Payment Intent (Embedded Stripe Elements)

**Endpoint:** `POST /payments/create-payment-intent`

**Description:** Creates a Stripe payment intent for embedded payment forms

**Request Body:**

```json
{
  "licenseType": "standard",
  "additionalDevices": 0,
  "email": "<EMAIL>",
  "deviceId": "optional-device-id"
}
```

**Response (200 OK):**

```json
{
  "clientSecret": "pi_1234567890abcdef_secret_xyz",
  "amount": 499,
  "licenseType": "standard",
  "paymentIntentId": "pi_1234567890abcdef"
}
```

#### 3. Create Checkout Session (Stripe Checkout Redirect)

**Endpoint:** `POST /payments/create-checkout-session`

**Description:** Creates a Stripe Checkout session for redirect-based payments

**Request Body:**

```json
{
  "licenseType": "standard",
  "additionalDevices": 0,
  "email": "<EMAIL>",
  "deviceId": "optional-device-id",
  "successUrl": "https://yourapp.com/success",
  "cancelUrl": "https://yourapp.com/cancel"
}
```

**Response (200 OK):**

```json
{
  "sessionId": "cs_1234567890abcdef",
  "url": "https://checkout.stripe.com/pay/cs_1234567890abcdef",
  "amount": 499,
  "licenseType": "standard"
}
```

#### 4. Get Checkout Session Status

**Endpoint:** `GET /payments/checkout-session/{sessionId}`

**Description:** Get status and details of a checkout session

**Response (200 OK):**

```json
{
  "sessionId": "cs_1234567890abcdef",
  "paymentStatus": "paid",
  "customerEmail": "<EMAIL>",
  "amountTotal": 499,
  "currency": "usd",
  "license": {
    "licenseKey": "ABCD-EFGH-JKMN-PQRS-TUVW-XYZ2",
    "licenseType": "standard",
    "maxDevices": 2,
    "expiresAt": null,
    "email": "<EMAIL>"
  },
  "metadata": {
    "licenseType": "standard",
    "email": "<EMAIL>",
    "deviceId": "optional-device-id"
  }
}
```

#### 5. Get Payment Intent Status

**Endpoint:** `GET /payments/payment-intent/{paymentIntentId}`

**Description:** Get status and details of a payment intent

**Response (200 OK):**

```json
{
  "paymentIntentId": "pi_1234567890abcdef",
  "status": "succeeded",
  "amount": 499,
  "currency": "usd",
  "license": {
    "licenseKey": "ABCD-EFGH-JKMN-PQRS-TUVW-XYZ2",
    "licenseType": "standard",
    "maxDevices": 2,
    "expiresAt": null,
    "email": "<EMAIL>"
  },
  "metadata": {
    "licenseType": "standard",
    "email": "<EMAIL>",
    "deviceId": "optional-device-id"
  }
}
```

### License Endpoints

#### 6. Create License (Manual Creation)

**Endpoint:** `POST /licenses/create`

**Description:** Creates a new license (requires payment verification for paid licenses)

**Headers:**

```
Content-Type: application/json
```

**Request Body:**

```json
{
  "email": "<EMAIL>",
  "licenseType": "standard",
  "deviceId": "optional-device-id",
  "stripePaymentIntentId": "pi_1234567890abcdef"
}
```

**Important Notes:**
- **deviceId is optional for paid licenses** - Paid licenses can be created without a deviceId (e.g., via Stripe webhooks) and the device will be registered later during license validation
- **deviceId is required for trial licenses** - Trial licenses must include a deviceId for immediate device registration to prevent abuse
- **License key format** - License keys are now 24-character alphanumeric strings (uppercase letters and numbers, excluding confusing characters like 0, O, 1, I, L)
- **Email delivery behavior**:
  - **Trial licenses**: No email sent, license key returned immediately in API response for instant access
  - **Paid licenses**: License key sent via email AND included in API response for flexibility

**Response (201 Created):**

**For Trial Licenses:**
```json
{
  "message": "Trial license created successfully. You can start using the app immediately with the license key provided below.",
  "license": {
    "id": "cmdp9be460000xxb9qhzalojd",
    "licenseKey": "DM6YW279J97QSKKH7YV25S8M",
    "email": "<EMAIL>",
    "licenseType": "trial",
    "maxDevices": 1,
    "expiresAt": "2025-08-13T00:42:16.904Z",
    "createdAt": "2025-07-30T00:42:16.904Z",
    "updatedAt": "2025-07-30T00:42:16.904Z",
    "stripePaymentIntentId": null,
    "devicesUsed": 1,
    "devices": [
      {
        "id": "device123",
        "firstSeen": "2025-07-30T00:42:16.904Z",
        "lastSeen": "2025-07-30T00:42:16.904Z",
        "appVersion": "1.0.0",
        "isActive": true
      }
    ]
  },
  "licenseKey": "DM6YW279J97QSKKH7YV25S8M",
  "licenseType": "trial",
  "maxDevices": 1,
  "expiresAt": "2025-08-13T00:42:16.904Z",
  "devicesUsed": 1,
  "trialInfo": {
    "emailSent": false,
    "immediateAccess": true,
    "note": "Trial license is ready for immediate use. No email verification required."
  }
}
```

**For Paid Licenses (Standard/Extended):**
```json
{
  "message": "License created successfully. The license key has been sent to your email address.",
  "licenseKey": "ABCD-EFGH-JKMN-PQRS-TUVW-XYZ2",
  "licenseType": "standard",
  "maxDevices": 2,
  "expiresAt": null,
  "devicesUsed": 1,
  "deliveryInfo": {
    "emailSent": true,
    "note": "License key has been sent to your email address. Please check your inbox."
  }
}
```

**Key Differences:**
- **Trial licenses**: License key returned immediately in response, no email sent
- **Paid licenses**: License key sent via email, also included in response for immediate use
- **Complete license object**: Both response types now include a complete `license` object with all database fields for immediate use
- **Backward compatibility**: Legacy fields (`licenseKey`, `licenseType`, etc.) are maintained at the root level

#### Trial License Restrictions

**Important:** To prevent abuse, the system enforces restrictions on trial license creation:

**Device ID Requirement:**
- **Trial licenses MUST include a deviceId** in the request body
- This enables immediate device registration and prevents abuse
- Attempting to create a trial license without deviceId returns `VALIDATION_ERROR`

**Email-based Restriction:**
- Each email address can only be used for one trial license
- Attempting to create a second trial with the same email returns `TRIAL_ALREADY_USED_EMAIL`

**Device-based Restriction:**
- Each device can only be used for one trial license
- Attempting to create a second trial on the same device returns `TRIAL_ALREADY_USED_DEVICE`

**Combined Restriction:**
- If both email and device have been used for trials, returns `TRIAL_ALREADY_USED`

**Trial-to-Paid Upgrade Path:**
- Users can seamlessly upgrade from trial to paid licenses without restriction
- When a user with an existing trial license purchases a paid license, the system:
  1. **Upgrades the existing trial license** instead of creating a new one
  2. **Preserves the original license key** for continuity
  3. **Maintains all registered devices** from the trial
  4. **Removes the expiration date** (converts to lifetime license)
  5. **Increases the device limit** based on the purchased license type
  6. **Logs the upgrade** for audit purposes

**Example Upgrade Flow:**
1. User creates trial license: `trial_abc123` (1 device, 14-day expiration)
2. User purchases standard license with same email
3. System upgrades `trial_abc123` to standard (2 devices, no expiration)
4. User continues using the same license key seamlessly

**Best Practices:**
- Always handle trial restriction errors gracefully
- Provide clear messaging to users about trial limitations
- Offer upgrade paths to paid licenses when trial restrictions are encountered
- Consider implementing user-friendly error messages that guide users toward purchasing
- When users have existing trials, guide them to purchase rather than create new trials

**Example Error Response:**

```json
{
  "error": "Trial license already used",
  "code": "TRIAL_ALREADY_USED_EMAIL",
  "details": "A trial license has already been used with this email address. Each email can only be used for one trial license.",
  "metadata": {
    "email": "<EMAIL>"
  }
}
```

#### License Activation Flows

The system supports two distinct license activation flows:

**Trial License Flow:**
1. User requests trial license in app
2. App calls `/licenses/create` with `deviceId` included
3. License created and device registered immediately
4. User can start using the app right away

**Paid License Flow:**
1. User purchases license (via Stripe checkout or payment intent)
2. License created via webhook without `deviceId` (user not in app yet)
3. License key sent to user via email
4. User enters license key in app
5. App calls `/licenses/validate` which registers the device
6. User can start using the app

This dual-flow system ensures optimal user experience for both trial and paid scenarios.

#### 7. Validate License & Register Device

**Endpoint:** `POST /licenses/validate`

**Description:** Validates a license key and registers the device (for paid licenses) or validates existing device registration (for trial licenses)

**Headers:**

```
Content-Type: application/json
```

**Request Body:**

```json
{
  "licenseKey": "ABCD-EFGH-JKMN-PQRS-TUVW-XYZ2",
  "deviceId": "abc123def456...",
  "appVersion": "1.0.0"
}
```

**Response (200 OK):**

```json
{
  "valid": true,
  "deviceToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "licenseType": "standard",
  "expiresAt": null,
  "devicesUsed": 1,
  "maxDevices": 2
}
```

#### 8. Get License Status

**Endpoint:** `GET /licenses/status/{licenseKey}`

**Description:** Get detailed status information for a license

**Response (200 OK):**

```json
{
  "licenseKey": "ABCD-EFGH-JKMN-PQRS-TUVW-XYZ2",
  "licenseType": "standard",
  "email": "<EMAIL>",
  "createdAt": "2024-01-15T10:30:00Z",
  "expiresAt": null,
  "maxDevices": 2,
  "devicesUsed": 1,
  "isExpired": false,
  "isActive": true,
  "devices": [
    {
      "id": "device_123",
      "firstSeen": "2024-01-15T10:35:00Z",
      "lastSeen": "2024-01-15T14:20:00Z",
      "appVersion": "1.0.0",
      "isActive": true
    }
  ]
}
```

#### 9. Remove Device

**Endpoint:** `DELETE /licenses/devices/{deviceId}`

**Description:** Remove a device from a license

**Headers:**

```
Authorization: Bearer {deviceToken}
```

**Response (200 OK):**

```json
{
  "message": "Device removed successfully",
  "devicesRemaining": 1
}
```

#### 10. Resend License Email

**Endpoint:** `POST /licenses/resend`

**Description:** Resend license information to email

**Request Body:**

```json
{
  "email": "<EMAIL>"
}
```

**Response (200 OK):**

```json
{
  "message": "License information sent successfully"
}
```

## Swift Integration Examples

### Complete License Manager Class

```swift
import Foundation
import Combine

class LicenseManager: ObservableObject {
    @Published var isLicensed = false
    @Published var licenseInfo: LicenseInfo?
    @Published var errorMessage: String?

    private let baseURL = "https://your-domain.com/api"
    private let tokenManager = TokenManager()
    private var cancellables = Set<AnyCancellable>()

    struct LicenseInfo {
        let licenseKey: String
        let licenseType: String
        let expiresAt: Date?
        let devicesUsed: Int
        let maxDevices: Int
    }

    func validateLicense(licenseKey: String) {
        let deviceId = generateDeviceID()
        let appVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0.0"

        // Normalize license key for API (remove dashes)
        let normalizedLicenseKey = normalizeLicenseKeyForAPI(licenseKey)

        let request = LicenseValidationRequest(
            licenseKey: normalizedLicenseKey,
            deviceId: deviceId,
            appVersion: appVersion
        )

        performRequest(
            endpoint: "/licenses/validate",
            method: "POST",
            body: request
        )
        .sink(
            receiveCompletion: { [weak self] completion in
                if case .failure(let error) = completion {
                    self?.handleError(error)
                }
            },
            receiveValue: { [weak self] (response: LicenseValidationResponse) in
                if response.valid {
                    self?.tokenManager.storeDeviceToken(response.deviceToken)
                    self?.isLicensed = true
                    self?.licenseInfo = LicenseInfo(
                        licenseKey: formatLicenseKeyForDisplay(normalizedLicenseKey),
                        licenseType: response.licenseType,
                        expiresAt: response.expiresAt,
                        devicesUsed: response.devicesUsed,
                        maxDevices: response.maxDevices
                    )
                } else {
                    self?.errorMessage = "Invalid license key"
                }
            }
        )
        .store(in: &cancellables)
    }

    private func performRequest<T: Codable, U: Codable>(
        endpoint: String,
        method: String,
        body: T? = nil
    ) -> AnyPublisher<U, Error> {
        guard let url = URL(string: baseURL + endpoint) else {
            return Fail(error: URLError(.badURL))
                .eraseToAnyPublisher()
        }

        var request = URLRequest(url: url)
        request.httpMethod = method
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")

        if let body = body {
            do {
                request.httpBody = try JSONEncoder().encode(body)
            } catch {
                return Fail(error: error)
                    .eraseToAnyPublisher()
            }
        }

        return URLSession.shared.dataTaskPublisher(for: request)
            .map(\.data)
            .decode(type: U.self, decoder: JSONDecoder())
            .receive(on: DispatchQueue.main)
            .eraseToAnyPublisher()
    }

    private func handleError(_ error: Error) {
        DispatchQueue.main.async {
            self.errorMessage = error.localizedDescription
        }
    }
}
```

### Request/Response Models

```swift
// Payment Request Models
struct PaymentIntentRequest: Codable {
    let licenseType: String
    let additionalDevices: Int?
    let email: String
    let deviceId: String?
}

struct CheckoutSessionRequest: Codable {
    let licenseType: String
    let additionalDevices: Int?
    let email: String
    let deviceId: String?
    let successUrl: String
    let cancelUrl: String
}

// License Request Models
struct LicenseValidationRequest: Codable {
    let licenseKey: String
    let deviceId: String
    let appVersion: String
}

struct LicenseCreationRequest: Codable {
    let email: String
    let licenseType: String
    let deviceId: String?
    let stripePaymentIntentId: String?
}

struct ResendLicenseRequest: Codable {
    let email: String
}

// Payment Response Models
struct PaymentIntentResponse: Codable {
    let clientSecret: String
    let amount: Int
    let licenseType: String
    let paymentIntentId: String
}

struct CheckoutSessionResponse: Codable {
    let sessionId: String
    let url: String
    let amount: Int
    let licenseType: String
}

struct CheckoutSessionStatusResponse: Codable {
    let sessionId: String
    let paymentStatus: String
    let customerEmail: String
    let amountTotal: Int
    let currency: String
    let license: LicenseInfo?
    let metadata: [String: String]
}

struct PaymentIntentStatusResponse: Codable {
    let paymentIntentId: String
    let status: String
    let amount: Int
    let currency: String
    let license: LicenseInfo?
    let metadata: [String: String]
}

struct PricingResponse: Codable {
    let trial: PricingTier
    let standard: PricingTier
    let extended: PricingTier
    let additionalDevice: AdditionalDevicePricing
}

struct PricingTier: Codable {
    let price: Int
    let maxDevices: Int
    let duration: String
}

struct AdditionalDevicePricing: Codable {
    let price: Int
    let description: String
}

// License Response Models
struct LicenseValidationResponse: Codable {
    let valid: Bool
    let deviceToken: String?
    let licenseType: String?
    let expiresAt: Date?
    let devicesUsed: Int?
    let maxDevices: Int?
}

struct LicenseCreationResponse: Codable {
    let licenseKey: String
    let licenseType: String
    let email: String
    let maxDevices: Int
    let expiresAt: Date?
    let createdAt: Date
}

struct LicenseStatusResponse: Codable {
    let licenseKey: String
    let licenseType: String
    let email: String
    let createdAt: Date
    let expiresAt: Date?
    let maxDevices: Int
    let devicesUsed: Int
    let isExpired: Bool
    let isActive: Bool
    let devices: [DeviceInfo]
}

struct DeviceInfo: Codable {
    let id: String
    let firstSeen: Date
    let lastSeen: Date
    let appVersion: String
    let isActive: Bool
}

struct ErrorResponse: Codable {
    let error: String
    let code: String?
    let details: String?
    let retryAfter: Int?
}
```

### Trial License Creation with Restriction Handling

```swift
class TrialLicenseManager: ObservableObject {
    @Published var creationState: CreationState = .idle
    @Published var errorMessage: String?

    enum CreationState {
        case idle
        case creating
        case success(LicenseCreationResponse)
        case failed(LicenseError)
    }

    func createTrialLicense(email: String) async {
        await MainActor.run {
            self.creationState = .creating
            self.errorMessage = nil
        }

        let deviceId = generateDeviceID()
        let request = LicenseCreationRequest(
            email: email,
            licenseType: "trial",
            deviceId: deviceId,
            stripePaymentIntentId: nil
        )

        do {
            let response: LicenseCreationResponse = try await networkService.request(
                endpoint: "/licenses/create",
                method: .POST,
                body: request
            )

            await MainActor.run {
                self.creationState = .success(response)
            }

        } catch let error as NetworkError {
            let licenseError = mapToLicenseError(error)

            await MainActor.run {
                self.creationState = .failed(licenseError)
                self.errorMessage = licenseError.errorDescription
            }
        }
    }

    private func mapToLicenseError(_ networkError: NetworkError) -> LicenseError {
        guard case .apiError(let code, let message, _) = networkError else {
            return .networkError(networkError)
        }

        switch code {
        case "TRIAL_ALREADY_USED_EMAIL":
            return .trialAlreadyUsedEmail
        case "TRIAL_ALREADY_USED_DEVICE":
            return .trialAlreadyUsedDevice
        case "TRIAL_ALREADY_USED":
            return .trialAlreadyUsed
        case "VALIDATION_ERROR":
            return .validationFailed(message)
        case "RATE_LIMIT_EXCEEDED":
            return .rateLimited(retryAfter: 60)
        default:
            return .networkError(networkError)
        }
    }
}

// Usage in SwiftUI
struct TrialLicenseView: View {
    @StateObject private var trialManager = TrialLicenseManager()
    @State private var email = ""
    @State private var showingUpgradeOptions = false

    var body: some View {
        VStack(spacing: 20) {
            TextField("Email Address", text: $email)
                .textFieldStyle(RoundedBorderTextFieldStyle())

            Button("Start Free Trial") {
                Task {
                    await trialManager.createTrialLicense(email: email)
                }
            }
            .disabled(email.isEmpty || trialManager.creationState == .creating)

            switch trialManager.creationState {
            case .idle:
                EmptyView()

            case .creating:
                ProgressView("Creating trial license...")

            case .success(let license):
                VStack {
                    Text("Trial license created successfully!")
                        .foregroundColor(.green)
                    Text("License Key: \(license.licenseKey)")
                        .font(.monospaced(.body)())
                }

            case .failed(let error):
                VStack {
                    Text(error.errorDescription ?? "Unknown error")
                        .foregroundColor(.red)

                    if case .trialAlreadyUsedEmail = error,
                       case .trialAlreadyUsedDevice = error,
                       case .trialAlreadyUsed = error {
                        Button("Upgrade to Paid License") {
                            showingUpgradeOptions = true
                        }
                        .buttonStyle(.borderedProminent)
                    }
                }
            }
        }
        .padding()
        .sheet(isPresented: $showingUpgradeOptions) {
            PurchaseLicenseView(email: email)
        }
    }
}
```

### Trial-to-Paid License Upgrades

The system automatically handles trial-to-paid license upgrades when users purchase a paid license with the same email address as their existing trial. Here's how to implement this in your Swift app:

```swift
class LicenseUpgradeManager: ObservableObject {
    @Published var upgradeState: UpgradeState = .idle
    @Published var currentLicense: LicenseInfo?

    enum UpgradeState {
        case idle
        case upgrading
        case success(LicenseInfo)
        case failed(Error)
    }

    func upgradeToPaidLicense(email: String, licenseType: String, paymentIntentId: String) async {
        await MainActor.run {
            upgradeState = .upgrading
        }

        do {
            let deviceId = await DeviceIdentifier.getDeviceId()

            let requestBody: [String: Any] = [
                "email": email,
                "licenseType": licenseType,
                "deviceId": deviceId,
                "stripePaymentIntentId": paymentIntentId
            ]

            let response = try await APIClient.shared.createLicense(requestBody)

            await MainActor.run {
                // The response will indicate if this was an upgrade
                if let message = response["message"] as? String,
                   message.contains("upgraded") {
                    print("Successfully upgraded trial license to \(licenseType)")
                } else {
                    print("Created new \(licenseType) license")
                }

                let licenseInfo = LicenseInfo(
                    licenseKey: response["licenseKey"] as! String,
                    licenseType: response["licenseType"] as! String,
                    maxDevices: response["maxDevices"] as! Int,
                    expiresAt: response["expiresAt"] as? String,
                    devicesUsed: response["devicesUsed"] as! Int
                )

                upgradeState = .success(licenseInfo)
                currentLicense = licenseInfo
            }
        } catch {
            await MainActor.run {
                upgradeState = .failed(error)
            }
        }
    }
}
```

**Key Benefits of the Upgrade System:**
- **Seamless Experience**: Users keep their existing license key
- **Device Continuity**: All registered devices remain active
- **No Data Loss**: Trial usage history is preserved
- **Automatic Conversion**: No manual intervention required

**Example Usage in SwiftUI:**

```swift
struct UpgradeView: View {
    @StateObject private var upgradeManager = LicenseUpgradeManager()
    let trialEmail: String

    var body: some View {
        VStack(spacing: 20) {
            Text("Upgrade Your Trial License")
                .font(.title2)
                .fontWeight(.semibold)

            Text("Your trial will be upgraded to a full license with the same license key.")
                .multilineTextAlignment(.center)
                .foregroundColor(.secondary)

            Button("Upgrade to Standard ($4.99)") {
                Task {
                    // After successful Stripe payment
                    await upgradeManager.upgradeToPaidLicense(
                        email: trialEmail,
                        licenseType: "standard",
                        paymentIntentId: "pi_stripe_payment_id"
                    )
                }
            }
            .buttonStyle(.borderedProminent)

            switch upgradeManager.upgradeState {
            case .upgrading:
                ProgressView("Upgrading license...")
            case .success(let license):
                VStack {
                    Text("✅ License upgraded successfully!")
                        .foregroundColor(.green)
                    Text("Same license key: \(license.licenseKey)")
                        .font(.monospaced(.caption))
                }
            case .failed(let error):
                Text("Upgrade failed: \(error.localizedDescription)")
                    .foregroundColor(.red)
            case .idle:
                EmptyView()
            }
        }
        .padding()
    }
}
```

## Error Handling

### Standard Error Response Format

All API errors follow this format:

```json
{
  "error": "Human-readable error message",
  "code": "ERROR_CODE",
  "details": "Additional error details",
  "retryAfter": 60
}
```

### Error Codes

| Code                    | Description                        | HTTP Status |
| ----------------------- | ---------------------------------- | ----------- |
| `VALIDATION_ERROR`      | Request validation failed          | 400         |
| `UNAUTHORIZED`          | Missing or invalid authentication  | 401         |
| `INVALID_TOKEN`         | Device token is invalid or expired | 401         |
| `NOT_FOUND`             | Resource not found                 | 404         |
| `LICENSE_NOT_FOUND`     | License key not found              | 404         |
| `DEVICE_NOT_REGISTERED` | Device not registered with license | 404         |
| `SESSION_NOT_FOUND`     | Checkout session not found        | 404         |
| `PAYMENT_INTENT_NOT_FOUND` | Payment intent not found        | 404         |
| `LICENSE_EXPIRED`       | License has expired                | 410         |
| `MAX_DEVICES_REACHED`   | Maximum devices limit reached      | 409         |
| `TRIAL_ALREADY_USED_EMAIL` | Trial license already used with this email | 409 |
| `TRIAL_ALREADY_USED_DEVICE` | Trial license already used on this device | 409 |
| `TRIAL_ALREADY_USED`    | Trial license already used (email and device) | 409 |
| `PAYMENT_REQUIRED`      | Payment verification failed        | 402         |
| `PAYMENT_FAILED`        | Payment processing failed          | 402         |
| `PAYMENT_INCOMPLETE`    | Payment not yet completed          | 402         |
| `CHECKOUT_SESSION_EXPIRED` | Checkout session has expired    | 410         |
| `RATE_LIMIT_EXCEEDED`   | Too many requests                  | 429         |
| `INTERNAL_ERROR`        | Server internal error              | 500         |

### Swift Error Handling Example

```swift
enum LicenseError: LocalizedError {
    case validationFailed(String)
    case rateLimited(retryAfter: Int)
    case unauthorized
    case maxDevicesReached
    case licenseExpired
    case trialAlreadyUsedEmail
    case trialAlreadyUsedDevice
    case trialAlreadyUsed
    case paymentRequired
    case paymentFailed(String)
    case paymentIncomplete
    case sessionNotFound
    case sessionExpired
    case networkError(Error)

    var errorDescription: String? {
        switch self {
        case .validationFailed(let message):
            return "Validation failed: \(message)"
        case .rateLimited(let retryAfter):
            return "Too many requests. Please wait \(retryAfter) seconds."
        case .unauthorized:
            return "Authentication failed. Please re-validate your license."
        case .maxDevicesReached:
            return "Maximum number of devices reached for this license."
        case .licenseExpired:
            return "License has expired. Please renew your license."
        case .trialAlreadyUsedEmail:
            return "A trial license has already been used with this email address."
        case .trialAlreadyUsedDevice:
            return "A trial license has already been used on this device."
        case .trialAlreadyUsed:
            return "A trial license has already been used with this email and device."
        case .paymentRequired:
            return "Payment is required to create this license."
        case .paymentFailed(let message):
            return "Payment failed: \(message)"
        case .paymentIncomplete:
            return "Payment is still being processed. Please wait."
        case .sessionNotFound:
            return "Payment session not found. Please try again."
        case .sessionExpired:
            return "Payment session has expired. Please start a new payment."
        case .networkError(let error):
            return "Network error: \(error.localizedDescription)"
        }
    }
}

func handleLicenseValidation() async {
    do {
        let response: LicenseValidationResponse = try await networkService.request(
            endpoint: "/licenses/validate",
            method: .POST,
            body: validationRequest
        )

        await MainActor.run {
            self.isLicensed = response.valid
        }

    } catch NetworkError.badRequest(let message) {
        throw LicenseError.validationFailed(message)

    } catch NetworkError.rateLimited(let retryAfter) {
        throw LicenseError.rateLimited(retryAfter: retryAfter)

    } catch NetworkError.unauthorized {
        throw LicenseError.unauthorized

    } catch {
        throw LicenseError.networkError(error)
    }
}
```

## Rate Limiting & Security

### Rate Limits

| Endpoint             | Limit        | Window     |
| -------------------- | ------------ | ---------- |
| `/licenses/validate` | 10 requests  | 15 minutes |
| `/licenses/create`   | 5 requests   | 15 minutes |
| `/licenses/resend`   | 3 requests   | 15 minutes |
| General API          | 100 requests | 15 minutes |

### Security Headers

Include these headers in your requests:

```swift
// User Agent identification
request.setValue("SnapbackApp/1.0 (macOS)", forHTTPHeaderField: "User-Agent")

// Device token for authenticated requests
if let token = tokenManager.getDeviceToken() {
    request.setValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
}
```

### Request Signing (Optional)

For enhanced security, implement request signing:

```swift
import CryptoKit

func signRequest(_ request: inout URLRequest, secret: String) {
    let timestamp = String(Int(Date().timeIntervalSince1970 * 1000))
    let body = request.httpBody.map { String(data: $0, encoding: .utf8) ?? "" } ?? ""
    let payload = timestamp + body

    let key = SymmetricKey(data: Data(secret.utf8))
    let signature = HMAC<SHA256>.authenticationCode(for: Data(payload.utf8), using: key)
    let signatureHex = Data(signature).map { String(format: "%02x", $0) }.joined()

    request.setValue(signatureHex, forHTTPHeaderField: "X-Request-Signature")
    request.setValue(timestamp, forHTTPHeaderField: "X-Request-Timestamp")
}
```

## Payment Integration

The license server supports two payment flows for different use cases:

### Payment Flow Options

#### Option 1: Stripe Checkout Redirect (Recommended for Web)
- Redirects users to Stripe-hosted payment page
- Automatic license creation after successful payment
- Less customization but faster implementation
- Mobile-optimized checkout experience

#### Option 2: Embedded Stripe Elements (Advanced)
- Payment form embedded directly in your application
- Full control over UI/UX
- Requires manual license creation after payment
- More complex but highly customizable

### Webhook Configuration

Configure your Stripe webhook to send events to:
```
https://your-license-server.com/api/payments/webhook
```

Required events:
- `payment_intent.succeeded`
- `payment_intent.payment_failed`
- `checkout.session.completed`

### Automatic License Creation

The license server automatically creates licenses for successful payments:

- **Stripe Checkout Redirect**: Licenses are created automatically when the `checkout.session.completed` webhook is received
- **Embedded Stripe Elements**: Licenses must be created manually via the `/licenses/create` endpoint after payment confirmation

This ensures a seamless user experience where customers receive their licenses immediately after successful payment.

### Swift Payment Integration Examples

#### Option 1: Stripe Checkout Redirect Flow

```swift
struct CheckoutSessionRequest: Codable {
    let licenseType: String
    let email: String
    let deviceId: String?
    let successUrl: String
    let cancelUrl: String
}

struct CheckoutSessionResponse: Codable {
    let sessionId: String
    let url: String
    let amount: Int
    let licenseType: String
}

struct CheckoutSessionStatusResponse: Codable {
    let sessionId: String
    let paymentStatus: String
    let customerEmail: String
    let amountTotal: Int
    let currency: String
    let license: LicenseInfo?
    let metadata: [String: String]
}

class PaymentManager: ObservableObject {
    @Published var paymentState: PaymentState = .idle

    enum PaymentState {
        case idle
        case creatingSession
        case redirectingToCheckout
        case processingPayment
        case completed(licenseKey: String)
        case cancelled
        case failed(Error)
    }

    func purchaseWithRedirect(licenseType: String, email: String, deviceId: String? = nil) async {
        await MainActor.run {
            self.paymentState = .creatingSession
        }

        do {
            let request = CheckoutSessionRequest(
                licenseType: licenseType,
                email: email,
                deviceId: deviceId,
                successUrl: "https://yourapp.com/payment-success",
                cancelUrl: "https://yourapp.com/payment-cancel"
            )

            let response: CheckoutSessionResponse = try await networkService.request(
                endpoint: "/payments/create-checkout-session",
                method: .POST,
                body: request
            )

            await MainActor.run {
                self.paymentState = .redirectingToCheckout
            }

            // Open Stripe Checkout URL in browser
            if let url = URL(string: response.url) {
                NSWorkspace.shared.open(url)
            }

            // Poll for payment completion (or use webhook)
            try await pollForPaymentCompletion(sessionId: response.sessionId)

        } catch {
            await MainActor.run {
                self.paymentState = .failed(error)
            }
        }
    }

    private func pollForPaymentCompletion(sessionId: String) async throws {
        for _ in 0..<30 { // Poll for up to 5 minutes
            try await Task.sleep(nanoseconds: 10_000_000_000) // 10 seconds

            let response: CheckoutSessionStatusResponse = try await networkService.request(
                endpoint: "/payments/checkout-session/\(sessionId)",
                method: .GET
            )

            if response.paymentStatus == "paid", let license = response.license {
                await MainActor.run {
                    self.paymentState = .completed(licenseKey: license.licenseKey)
                }
                return
            } else if response.paymentStatus == "cancelled" {
                await MainActor.run {
                    self.paymentState = .cancelled
                }
                return
            }
        }

        throw PaymentError.timeout
    }
}
```

#### Option 2: Embedded Stripe Elements Flow

```swift
struct PaymentIntentRequest: Codable {
    let licenseType: String
    let email: String
    let deviceId: String?
}

struct PaymentIntentResponse: Codable {
    let clientSecret: String
    let amount: Int
    let licenseType: String
    let paymentIntentId: String
}

func purchaseWithEmbeddedForm(licenseType: String, email: String, deviceId: String? = nil) async {
    await MainActor.run {
        self.paymentState = .creatingPaymentIntent
    }

    do {
        // Step 1: Create payment intent
        let intentRequest = PaymentIntentRequest(
            licenseType: licenseType,
            email: email,
            deviceId: deviceId
        )

        let intentResponse: PaymentIntentResponse = try await networkService.request(
            endpoint: "/payments/create-payment-intent",
            method: .POST,
            body: intentRequest
        )

        await MainActor.run {
            self.paymentState = .processingPayment
        }

        // Step 2: Process payment with Stripe SDK (implement Stripe integration)
        let confirmedPayment = try await confirmPaymentWithStripe(
            clientSecret: intentResponse.clientSecret
        )

        await MainActor.run {
            self.paymentState = .creatingLicense
        }

        // Step 3: Create license after successful payment
        let licenseRequest = LicenseCreationRequest(
            email: email,
            licenseType: licenseType,
            deviceId: deviceId,
            stripePaymentIntentId: intentResponse.paymentIntentId
        )

        let license: LicenseCreationResponse = try await networkService.request(
            endpoint: "/licenses/create",
            method: .POST,
            body: licenseRequest
        )

        await MainActor.run {
            self.paymentState = .completed(licenseKey: license.licenseKey)
        }

    } catch {
        await MainActor.run {
            self.paymentState = .failed(error)
        }
    }
}
```

```swift
struct CheckoutSessionRequest: Codable {
    let licenseType: String
    let email: String
    let deviceId: String?
    let successUrl: String
    let cancelUrl: String
}

struct CheckoutSessionResponse: Codable {
    let sessionId: String
    let url: String
    let amount: Int
    let licenseType: String
}

class PaymentManager: ObservableObject {
    @Published var paymentState: PaymentState = .idle

    enum PaymentState {
        case idle
        case creatingSession
        case redirectingToCheckout
        case processingPayment
        case completed(licenseKey: String)
        case cancelled
        case failed(Error)
    }

    func purchaseWithRedirect(licenseType: String, email: String, deviceId: String? = nil) async {
        await MainActor.run {
            self.paymentState = .creatingSession
        }

        do {
            let request = CheckoutSessionRequest(
                licenseType: licenseType,
                email: email,
                deviceId: deviceId,
                successUrl: "https://yourapp.com/payment-success",
                cancelUrl: "https://yourapp.com/payment-cancel"
            )

            let response: CheckoutSessionResponse = try await networkService.request(
                endpoint: "/payments/create-checkout-session",
                method: .POST,
                body: request
            )

            await MainActor.run {
                self.paymentState = .redirectingToCheckout
            }

            // Open Stripe Checkout URL in browser
            if let url = URL(string: response.url) {
                NSWorkspace.shared.open(url)
            }

            // Poll for payment completion (or use webhook)
            try await pollForPaymentCompletion(sessionId: response.sessionId)

        } catch {
            await MainActor.run {
                self.paymentState = .failed(error)
            }
        }
    }

    private func pollForPaymentCompletion(sessionId: String) async throws {
        for _ in 0..<30 { // Poll for up to 5 minutes
            try await Task.sleep(nanoseconds: 10_000_000_000) // 10 seconds

            let response: CheckoutSessionStatusResponse = try await networkService.request(
                endpoint: "/payments/checkout-session/\(sessionId)",
                method: .GET
            )

            if response.paymentStatus == "paid", let license = response.license {
                await MainActor.run {
                    self.paymentState = .completed(licenseKey: license.licenseKey)
                }
                return
            } else if response.paymentStatus == "cancelled" {
                await MainActor.run {
                    self.paymentState = .cancelled
                }
                return
            }
        }

        throw PaymentError.timeout
    }
}
```

#### Option 2: Embedded Stripe Elements Flow

```swift
struct PaymentIntentRequest: Codable {
    let licenseType: String
    let email: String
    let deviceId: String?
}

struct PaymentIntentResponse: Codable {
    let clientSecret: String
    let amount: Int
    let licenseType: String
    let paymentIntentId: String
}

func purchaseWithEmbeddedForm(licenseType: String, email: String, deviceId: String? = nil) async {
    await MainActor.run {
        self.paymentState = .creatingPaymentIntent
    }

    do {
        // Step 1: Create payment intent
        let intentRequest = PaymentIntentRequest(
            licenseType: licenseType,
            email: email,
            deviceId: deviceId
        )

        let intentResponse: PaymentIntentResponse = try await networkService.request(
            endpoint: "/payments/create-payment-intent",
            method: .POST,
            body: intentRequest
        )

        await MainActor.run {
            self.paymentState = .processingPayment
        }

        // Step 2: Process payment with Stripe SDK (implement Stripe integration)
        let confirmedPayment = try await confirmPaymentWithStripe(
            clientSecret: intentResponse.clientSecret
        )

        await MainActor.run {
            self.paymentState = .creatingLicense
        }

        // Step 3: Create license after successful payment
        let licenseRequest = LicenseCreationRequest(
            email: email,
            licenseType: licenseType,
            deviceId: deviceId,
            stripePaymentIntentId: intentResponse.paymentIntentId
        )

        let license: LicenseCreationResponse = try await networkService.request(
            endpoint: "/licenses/create",
            method: .POST,
            body: licenseRequest
        )

        await MainActor.run {
            self.paymentState = .completed(licenseKey: license.licenseKey)
        }

    } catch {
        await MainActor.run {
            self.paymentState = .failed(error)
        }
    }
}
```

### Complete Purchase Flow

```swift
class PurchaseManager: ObservableObject {
    @Published var purchaseState: PurchaseState = .idle

    enum PurchaseState {
        case idle
        case creatingPaymentIntent
        case processingPayment
        case creatingLicense
        case completed(licenseKey: String)
        case failed(Error)
    }

    func purchaseLicense(licenseType: String, email: String) async {
        await MainActor.run {
            self.purchaseState = .creatingPaymentIntent
        }

        do {
            // Step 1: Create payment intent
            let paymentIntent = try await createPaymentIntent(
                licenseType: licenseType,
                email: email
            )

            await MainActor.run {
                self.purchaseState = .processingPayment
            }

            // Step 2: Process payment with Stripe (implement Stripe SDK integration)
            let confirmedPayment = try await confirmPayment(
                clientSecret: paymentIntent.clientSecret
            )

            await MainActor.run {
                self.purchaseState = .creatingLicense
            }

            // Step 3: Create license after successful payment
            let licenseRequest = LicenseCreationRequest(
                email: email,
                licenseType: licenseType,
                paymentIntentId: paymentIntent.paymentIntentId
            )

            let license: LicenseCreationResponse = try await networkService.request(
                endpoint: "/licenses",
                method: .POST,
                body: licenseRequest
            )

            await MainActor.run {
                self.purchaseState = .completed(licenseKey: license.licenseKey)
            }

        } catch {
            await MainActor.run {
                self.purchaseState = .failed(error)
            }
        }
    }

    private func confirmPayment(clientSecret: String) async throws -> String {
        // Implement Stripe payment confirmation
        // This is a placeholder - use actual Stripe SDK
        return "payment_confirmed"
    }
}
```

## Troubleshooting

### Common Issues

#### 1. License Validation Fails

**Problem:** License key is valid but validation returns `false`

**Solutions:**

- Check device ID generation consistency
- Verify app version format (semantic versioning)
- Ensure license hasn't reached device limit
- Check for expired trial licenses

```swift
func debugLicenseValidation() {
    let deviceId = generateDeviceID()
    print("Device ID: \(deviceId)")
    print("App Version: \(Bundle.main.infoDictionary?["CFBundleShortVersionVersion"] as? String ?? "unknown")")

    // Test with a known valid license
    validateLicense(licenseKey: "test_license_key")
}
```

#### 2. Rate Limiting Issues

**Problem:** Getting 429 errors frequently

**Solutions:**

- Implement exponential backoff
- Cache validation results
- Avoid unnecessary API calls

```swift
class RateLimitHandler {
    private var lastValidation: Date?
    private let validationCacheTime: TimeInterval = 300 // 5 minutes

    func shouldValidate() -> Bool {
        guard let lastValidation = lastValidation else { return true }
        return Date().timeIntervalSince(lastValidation) > validationCacheTime
    }

    func recordValidation() {
        lastValidation = Date()
    }
}
```

#### 3. Network Connectivity Issues

**Problem:** API calls fail due to network issues

**Solutions:**

- Implement retry logic with exponential backoff
- Handle offline scenarios gracefully
- Provide clear error messages to users

```swift
func validateLicenseWithRetry(maxRetries: Int = 3) async throws {
    var lastError: Error?

    for attempt in 1...maxRetries {
        do {
            try await validateLicense()
            return // Success
        } catch NetworkError.networkError(let error) {
            lastError = error

            if attempt < maxRetries {
                let delay = pow(2.0, Double(attempt)) // Exponential backoff
                try await Task.sleep(nanoseconds: UInt64(delay * 1_000_000_000))
            }
        } catch {
            throw error // Don't retry for non-network errors
        }
    }

    throw lastError ?? NetworkError.unknownError(0)
}
```

### Debug Mode

Enable debug logging for development:

```swift
class APILogger {
    static let shared = APILogger()
    private let isDebugMode = ProcessInfo.processInfo.environment["DEBUG_API"] == "1"

    func log(_ message: String) {
        if isDebugMode {
            print("[API Debug] \(message)")
        }
    }

    func logRequest(_ request: URLRequest) {
        guard isDebugMode else { return }

        print("[API Request] \(request.httpMethod ?? "GET") \(request.url?.absoluteString ?? "")")

        if let headers = request.allHTTPHeaderFields {
            print("[API Headers] \(headers)")
        }

        if let body = request.httpBody,
           let bodyString = String(data: body, encoding: .utf8) {
            print("[API Body] \(bodyString)")
        }
    }

    func logResponse(_ data: Data, _ response: URLResponse) {
        guard isDebugMode else { return }

        if let httpResponse = response as? HTTPURLResponse {
            print("[API Response] Status: \(httpResponse.statusCode)")
        }

        if let responseString = String(data: data, encoding: .utf8) {
            print("[API Response Body] \(responseString)")
        }
    }
}
```

### Testing

Create unit tests for your license management:

```swift
import XCTest
@testable import YourApp

class LicenseManagerTests: XCTestCase {
    var licenseManager: LicenseManager!
    var mockNetworkService: MockNetworkService!

    override func setUp() {
        super.setUp()
        mockNetworkService = MockNetworkService()
        licenseManager = LicenseManager(networkService: mockNetworkService)
    }

    func testValidLicenseValidation() async throws {
        // Arrange
        let expectedResponse = LicenseValidationResponse(
            valid: true,
            deviceToken: "mock_token",
            licenseType: "standard",
            expiresAt: nil,
            devicesUsed: 1,
            maxDevices: 2
        )
        mockNetworkService.mockResponse = expectedResponse

        // Act
        try await licenseManager.validateLicense(licenseKey: "test_key")

        // Assert
        XCTAssertTrue(licenseManager.isLicensed)
        XCTAssertEqual(licenseManager.licenseInfo?.licenseType, "standard")
    }

    func testInvalidLicenseValidation() async throws {
        // Arrange
        mockNetworkService.mockError = NetworkError.badRequest("Invalid license key")

        // Act & Assert
        do {
            try await licenseManager.validateLicense(licenseKey: "invalid_key")
            XCTFail("Expected error to be thrown")
        } catch {
            XCTAssertFalse(licenseManager.isLicensed)
        }
    }
}
```

## Best Practices

### 1. Secure Storage

- Store device tokens in Keychain
- Never store license keys in plain text
- Use app-specific keychain groups
- Never store Stripe keys in client applications

### 2. User Experience

- Provide clear error messages
- Implement offline license validation caching
- Show progress indicators during API calls
- Handle edge cases gracefully
- Provide clear payment status updates
- Allow users to retry failed payments

### 3. Performance

- Cache validation results appropriately
- Implement proper retry logic
- Use background queues for API calls
- Minimize unnecessary network requests
- Cache pricing information appropriately

### 4. Security

- Validate SSL certificates
- Implement certificate pinning for production
- Use request signing for sensitive operations
- Regularly rotate secrets and keys
- Never expose Stripe secret keys in client code
- Validate webhook signatures on the server

### 5. Payment Integration

- Always verify payment status before creating licenses
- Implement proper error handling for payment failures
- Use webhooks for reliable payment processing
- Provide clear payment confirmation to users
- Handle payment timeouts gracefully
- Test with Stripe's test cards before going live

This comprehensive guide provides everything needed to integrate the Snapback License Management API with a Swift macOS application, including complete code examples, error handling, security considerations, and troubleshooting guidance.
