{"name": "snapback-server", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"prepare": "husky", "dev": "pnpm -r dev", "build": "pnpm -r build", "check-types": "pnpm -r check-types", "dev:native": "pnpm --filter native dev", "dev:web": "pnpm --filter web dev", "dev:server": "pnpm --filter server dev", "db:push": "pnpm --filter server db:push", "db:studio": "pnpm --filter server db:studio", "db:generate": "pnpm --filter server db:generate", "db:migrate": "pnpm --filter server db:migrate", "db:seed": "pnpm --filter server db:seed", "db:reset": "pnpm --filter server db:reset", "db:start": "pnpm --filter server db:start", "db:watch": "pnpm --filter server db:watch", "db:stop": "pnpm --filter server db:stop", "db:down": "pnpm --filter server db:down"}, "devDependencies": {"@biomejs/biome": "^2.1.2", "husky": "^9.1.7", "lint-staged": "^15.5.0"}, "packageManager": "pnpm@9.15.2", "lint-staged": "{\"*.{js,ts,jsx,tsx}\": [\"eslint --fix\", \"prettier --write\"]}"}