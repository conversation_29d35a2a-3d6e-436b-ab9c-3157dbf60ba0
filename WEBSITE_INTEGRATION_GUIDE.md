# Website Integration Guide for License Server

This guide explains how to integrate your website with the snapback license server for handling license purchases.

## Payment Flow Options

The license server now supports **two payment flows**:

### 1. **Embedded Stripe Elements** (Recommended for custom UI)
- Payment form embedded directly in your website
- Full control over UI/UX
- Requires manual license creation after payment

### 2. **Stripe Checkout Redirect** (Recommended for quick setup)
- Redirects users to Stripe-hosted payment page
- Automatic license creation after successful payment
- Less customization but faster implementation

## API Endpoints Overview

### Payment Endpoints
- `GET /api/payments/pricing` - Get pricing information
- `POST /api/payments/create-payment-intent` - Create payment intent (embedded flow)
- `POST /api/payments/create-checkout-session` - Create checkout session (redirect flow)
- `GET /api/payments/checkout-session/:sessionId` - Get checkout session status
- `GET /api/payments/payment-intent/:paymentIntentId` - Get payment intent status

### License Endpoints
- `POST /api/licenses/create` - Create license (required for embedded flow)
- `POST /api/licenses/validate` - Validate license
- `POST /api/licenses/resend` - Resend license email

## Implementation Examples

### Option 1: Stripe Checkout Redirect Flow (Easiest)

#### Frontend Implementation
```javascript
// 1. Get pricing information
async function getPricing() {
  const response = await fetch('/api/payments/pricing');
  return response.json();
}

// 2. Create checkout session and redirect
async function purchaseLicense(licenseType, email, deviceId = null) {
  const response = await fetch('/api/payments/create-checkout-session', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      licenseType,
      email,
      deviceId,
      successUrl: `${window.location.origin}/success`,
      cancelUrl: `${window.location.origin}/cancel`
    })
  });
  
  const { url } = await response.json();
  
  // Redirect to Stripe Checkout
  window.location.href = url;
}

// 3. Handle success page
async function handleSuccess() {
  const urlParams = new URLSearchParams(window.location.search);
  const sessionId = urlParams.get('session_id');
  
  if (sessionId) {
    const response = await fetch(`/api/payments/checkout-session/${sessionId}`);
    const session = await response.json();
    
    if (session.license) {
      // License was automatically created!
      displayLicense(session.license);
    }
  }
}
```

#### HTML Example
```html
<!DOCTYPE html>
<html>
<head>
    <title>Purchase License</title>
</head>
<body>
    <div id="pricing-container">
        <h2>Choose Your License</h2>
        <div class="license-option" data-type="standard">
            <h3>Standard License</h3>
            <p>$29.99 - 2 Devices</p>
            <button onclick="purchaseLicense('standard')">Purchase</button>
        </div>
        <div class="license-option" data-type="extended">
            <h3>Extended License</h3>
            <p>$49.99 - 5 Devices</p>
            <button onclick="purchaseLicense('extended')">Purchase</button>
        </div>
    </div>

    <script>
        async function purchaseLicense(licenseType) {
            const email = prompt('Enter your email:');
            if (!email) return;
            
            try {
                const response = await fetch('/api/payments/create-checkout-session', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        licenseType,
                        email,
                        successUrl: `${window.location.origin}/success.html`,
                        cancelUrl: `${window.location.origin}/cancel.html`
                    })
                });
                
                const { url } = await response.json();
                window.location.href = url;
            } catch (error) {
                alert('Payment setup failed. Please try again.');
            }
        }
    </script>
</body>
</html>
```

### Option 2: Embedded Stripe Elements Flow (Advanced)

#### Frontend Implementation
```javascript
// Load Stripe.js
const stripe = Stripe('pk_test_your_publishable_key');

// 1. Create payment intent
async function createPaymentIntent(licenseType, email, deviceId = null) {
  const response = await fetch('/api/payments/create-payment-intent', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      licenseType,
      email,
      deviceId
    })
  });
  
  return response.json();
}

// 2. Handle payment form
async function handlePayment(licenseType, email) {
  const { clientSecret, paymentIntentId } = await createPaymentIntent(licenseType, email);
  
  const elements = stripe.elements();
  const cardElement = elements.create('card');
  cardElement.mount('#card-element');
  
  const { error, paymentIntent } = await stripe.confirmCardPayment(clientSecret, {
    payment_method: {
      card: cardElement,
      billing_details: { email }
    }
  });
  
  if (error) {
    console.error('Payment failed:', error);
    return;
  }
  
  if (paymentIntent.status === 'succeeded') {
    // 3. Create license after successful payment
    await createLicense(email, licenseType, paymentIntentId);
  }
}

// 3. Create license after payment
async function createLicense(email, licenseType, stripePaymentIntentId, deviceId = null) {
  const response = await fetch('/api/licenses/create', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      email,
      licenseType,
      deviceId,
      stripePaymentIntentId
    })
  });
  
  const license = await response.json();
  displayLicense(license);
}
```

## Success/Cancel Page Examples

### success.html
```html
<!DOCTYPE html>
<html>
<head>
    <title>Payment Successful</title>
</head>
<body>
    <div id="success-container">
        <h1>Payment Successful!</h1>
        <p>Your license has been created and sent to your email.</p>
        <div id="license-info"></div>
    </div>

    <script>
        async function loadLicenseInfo() {
            const urlParams = new URLSearchParams(window.location.search);
            const sessionId = urlParams.get('session_id');
            
            if (sessionId) {
                const response = await fetch(`/api/payments/checkout-session/${sessionId}`);
                const session = await response.json();
                
                if (session.license) {
                    document.getElementById('license-info').innerHTML = `
                        <h3>License Details</h3>
                        <p><strong>License Key:</strong> ${session.license.licenseKey}</p>
                        <p><strong>Type:</strong> ${session.license.licenseType}</p>
                        <p><strong>Max Devices:</strong> ${session.license.maxDevices}</p>
                        <p><strong>Email:</strong> ${session.license.email}</p>
                    `;
                }
            }
        }
        
        loadLicenseInfo();
    </script>
</body>
</html>
```

### cancel.html
```html
<!DOCTYPE html>
<html>
<head>
    <title>Payment Cancelled</title>
</head>
<body>
    <div id="cancel-container">
        <h1>Payment Cancelled</h1>
        <p>Your payment was cancelled. No charges were made.</p>
        <a href="/">Return to License Selection</a>
    </div>
</body>
</html>
```

## Environment Variables Required

Add these to your `.env` file:

```env
# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your_secret_key
STRIPE_PUBLIC_KEY=pk_test_your_public_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Your website URLs
CORS_ORIGIN=https://yourwebsite.com
```

## Webhook Configuration

Configure your Stripe webhook to send events to:
```
https://your-license-server.com/api/payments/webhook
```

Required events:
- `payment_intent.succeeded`
- `payment_intent.payment_failed`
- `checkout.session.completed`

## Next Steps

1. Choose your preferred payment flow (redirect vs embedded)
2. Implement the frontend code examples
3. Set up your success/cancel pages
4. Configure Stripe webhooks
5. Test with Stripe test keys
6. Deploy and switch to live keys

## Testing

Use Stripe's test card numbers:
- Success: `****************`
- Decline: `****************`
