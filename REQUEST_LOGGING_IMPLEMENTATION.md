# Request Logging Implementation

## Overview

Comprehensive request logging has been successfully implemented across all API endpoints in the snapback-server. This implementation provides detailed debugging information for all license and payment operations while maintaining security by redacting sensitive data.

## Implementation Details

### 1. New Logging System (`apps/server/src/utils/logger.ts`)

Created a structured logging utility with the following features:

- **Log Levels**: ERROR, WARN, INFO, DEBUG (configurable via LOG_LEVEL environment variable)
- **Endpoint Prefixes**: Unique prefixes for easy filtering in Xcode
- **Data Sanitization**: Automatic redaction of sensitive fields
- **Consistent Format**: Structured log messages with timestamp, level, prefix, and context

#### Endpoint Prefixes for Filtering:
- `[LICENSE_CREATE]` - License creation requests
- `[LICENSE_VALIDATE]` - License validation requests  
- `[LICENSE_STATUS]` - License status queries
- `[LICENSE_RESEND]` - License resend requests
- `[LICENSE_UPGRADE]` - License upgrade requests
- `[LICENSE_REMOVE_DEVICE]` - Device removal requests
- `[PAYMENT_CREATE_INTENT]` - Payment intent creation
- `[PAYMENT_CREATE_CHECKOUT]` - Checkout session creation
- `[PAYMENT_WEBHOOK]` - Stripe webhook processing
- `[PAYMENT_STATUS]` - Payment status queries
- `[PAYMENT_PRICING]` - Pricing information requests

### 2. Request Context Logging

Each endpoint now logs the following request details:

1. **Request Body**: `req.body` (with sensitive data redacted)
2. **URL Parameters**: `req.params` (route parameters like `:id`)
3. **Query Parameters**: `req.query` (URL query string parameters)
4. **Headers**: Key headers like Content-Type, Authorization, User-Agent
5. **HTTP Method**: `req.method`
6. **Request URL**: `req.url` or `req.originalUrl`
7. **IP Address**: `req.ip` for security tracking
8. **Response Time**: Time taken to process the request
9. **Status Code**: HTTP response status

### 3. Security Features

#### Data Sanitization
Sensitive fields are automatically redacted or truncated:
- `password`, `token`, `secret`, `key`, `authorization`
- `stripePaymentIntentId`, `deviceToken`, `jwt`
- License keys show only first 4 and last 4 characters
- Device IDs are marked as `[PROVIDED]` or `[NOT_PROVIDED]`

#### Log Structure Example
```
[2024-01-30T10:30:45.123Z] [INFO] [LICENSE_VALIDATE] Processing license validation request GET /api/licenses/validate (*************) - Body: {"licenseKey":"ABCD...XYZ2","deviceId":"[PROVIDED]","appVersion":"1.0.0"} UA: MyApp/1.0.0 Auth: Bear...ken1 200ms Status: 200
```

## Updated Endpoints

### License Endpoints (`apps/server/src/routes/license/index.ts`)

All license endpoints now include comprehensive logging:

1. **POST /api/licenses/create** - License creation with payment verification
2. **POST /api/licenses/validate** - License validation and device registration
3. **POST /api/licenses/resend** - License email resend
4. **POST /api/licenses/upgrade** - License upgrade with additional devices
5. **GET /api/licenses/status/:licenseKey** - License status queries
6. **DELETE /api/licenses/devices/:deviceId** - Device removal

### Payment Endpoints (`apps/server/src/routes/payment/index.ts`)

All payment endpoints now include comprehensive logging:

1. **POST /api/payments/create-payment-intent** - Payment intent creation
2. **POST /api/payments/create-checkout-session** - Checkout session creation
3. **POST /api/payments/create-upgrade-payment-intent** - Upgrade payment intent
4. **POST /api/payments/webhook** - Stripe webhook processing
5. **GET /api/payments/pricing** - Pricing information
6. **GET /api/payments/checkout-session/:sessionId** - Checkout session status
7. **GET /api/payments/payment-intent/:paymentIntentId** - Payment intent status

## Log Filtering in Xcode

To filter logs in Xcode console, use these search patterns:

### By Endpoint Type:
- `LICENSE_` - All license-related operations
- `PAYMENT_` - All payment-related operations

### By Specific Endpoint:
- `LICENSE_VALIDATE` - License validation only
- `PAYMENT_CREATE` - Payment creation only
- `PAYMENT_WEBHOOK` - Webhook processing only

### By Log Level:
- `[ERROR]` - Error messages only
- `[WARN]` - Warning messages only
- `[INFO]` - Informational messages only

### By IP Address:
- `(*************)` - Requests from specific IP

## Configuration

The logging system respects the existing `LOG_LEVEL` environment variable:
- `error` - Only error messages
- `warn` - Warnings and errors
- `info` - Info, warnings, and errors (default)
- `debug` - All messages including debug info

## Benefits

1. **Enhanced Debugging**: Complete visibility into request flow
2. **Security Tracking**: IP addresses and suspicious activity logging
3. **Performance Monitoring**: Response time tracking
4. **Easy Filtering**: Endpoint-specific prefixes for quick log filtering
5. **Data Protection**: Automatic sanitization of sensitive information
6. **Consistent Format**: Structured logs across all endpoints

## Testing

The implementation has been tested and builds successfully. All endpoints maintain their existing functionality while adding comprehensive logging capabilities.

## Next Steps

1. Monitor logs during development and testing
2. Adjust log levels as needed for different environments
3. Consider adding log aggregation for production environments
4. Review and refine sensitive data redaction rules as needed
