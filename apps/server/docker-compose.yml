name: snapback-server

services:
  postgres:
    image: postgres
    container_name: snapback-server-postgres
    environment:
      POSTGRES_DB: snapback-server
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - snapback-server_postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

volumes:
  snapback-server_postgres_data: