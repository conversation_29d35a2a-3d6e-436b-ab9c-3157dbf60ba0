import "dotenv/config";
import cors from "cors";
import express from "express";
import helmet from "helmet";
import { validateEnv } from "@/config/env";
import { generalApiLimit } from "@/middleware/rate-limit";
import licenseRouter from "@/routes/license";
import paymentRouter from "@/routes/payment";
import { errorHandler } from "@/utils/errors";

// Validate environment variables at startup
const env = validateEnv();

const app = express();

// Security middleware
app.use(
	helmet({
		contentSecurityPolicy: {
			directives: {
				defaultSrc: ["'self'"],
				scriptSrc: ["'self'", "js.stripe.com"],
				frameSrc: ["js.stripe.com"],
			},
		},
	}),
);
app.use(
	cors({
		origin: env.CORS_ORIGIN,
		methods: ["GET", "POST", "OPTIONS"],
	}),
);

// Body parser
app.use(express.json({ limit: "10mb" }));

// General rate limiting for all API routes
app.use("/api", generalApiLimit);

// Routes
app.use("/api/licenses", licenseRouter);
app.use("/api/payments", paymentRouter);

// Error handling middleware
app.use(errorHandler);

// 404 handler
app.use((_req, res) => {
	res.status(404).json({ error: "Endpoint not found" });
});

const port = env.PORT;
app.listen(port, () => {
	console.log(`Server is running on port ${port}`);
});
