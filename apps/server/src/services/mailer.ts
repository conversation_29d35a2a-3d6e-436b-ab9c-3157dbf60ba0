import nodemailer from "nodemailer";

/**
 * Email transporter configuration for SMTP
 * Configured with environment variables for host, port, and authentication
 */
const transporter = nodemailer.createTransport({
	host: process.env.SMTP_HOST,
	port: parseInt(process.env.SMTP_PORT as string),
	secure: parseInt(process.env.SMTP_PORT as string) === 465, // Use secure for port 465, false for 587
	auth: {
		user: process.env.SMTP_USER,
		pass: process.env.SMTP_PASS,
	},
});

export { transporter };
