import { transporter } from "@/services/mailer";

const sendLicenseEmail = async (
	email: string,
	licenseKey: string,
	licenseType: string,
	expiresAt: Date | null = null,
) => {
	const expirationText = expiresAt
		? `This trial license expires on ${new Date(
				expiresAt,
			).toLocaleDateString()}.`
		: "This is a permanent license.";

	const mailOptions = {
		from: process.env.FROM_EMAIL,
		to: email,
		subject: "Your App License Key",
		html: `
      <h2>Your License Key</h2>
      <p>Thank you for your purchase! Here's your license information:</p>
      
      <div style="background: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0;">
        <h3>License Key:</h3>
        <code style="font-size: 18px; font-weight: bold; color: #2563eb;">${licenseKey}</code>
      </div>
      
      <p><strong>License Type:</strong> ${licenseType.toUpperCase()}</p>
      <p><strong>Devices Allowed:</strong> ${
				licenseType === "standard"
					? "2"
					: licenseType === "extended"
						? "5"
						: "1"
			}</p>
      <p>${expirationText}</p>
      
      <p>Keep this email safe - you'll need your license key to activate the app.</p>
      
      <hr>
      <p><small>If you lose this email, you can request your license key again using the same email address.</small></p>
    `,
	};

	await transporter.sendMail(mailOptions);
};

export { sendLicenseEmail };
