import express, { Router } from "express";
import type Stripe from "stripe";
import stripe from "@/services/stripe";
import { Logger, EndpointPrefix, requestLoggingMiddleware, extractRequestContext } from "@/utils/logger";
import prisma from "../../../prisma";

const router: Router = Router();

// Payment configuration

const PRICING: Record<string, number> = {
  trial: 0,
  standard: 499, // $4.99 in cents
  extended: 999, // $9.99 in cents
  additionalDevice: 99, // $0.99 per additional device
};

// Create payment intent for license purchase (Embedded Stripe Elements)
router.post("/create-payment-intent", requestLoggingMiddleware(EndpointPrefix.PAYMENT_CREATE_INTENT), async (req, res) => {
  try {
    const context = extractRequestContext(req);
    Logger.info(EndpointPrefix.PAYMENT_CREATE_INTENT, "Processing payment intent creation", context);

    const { licenseType, additionalDevices = 0, email, deviceId } = req.body;

    Logger.info(EndpointPrefix.PAYMENT_CREATE_INTENT, `Payment intent requested: ${licenseType}`, {
      ...context,
      body: { licenseType, additionalDevices, email, deviceId: deviceId ? '[PROVIDED]' : '[NOT_PROVIDED]' }
    });

    if (!["standard", "extended"].includes(licenseType)) {
      Logger.error(EndpointPrefix.PAYMENT_CREATE_INTENT, `Invalid license type: ${licenseType}`, context);
      return res.status(400).json({ error: "Invalid license type" });
    }

    let amount = PRICING[licenseType];

    // Add cost for additional devices
    if (additionalDevices > 0) {
      amount += additionalDevices * PRICING.additionalDevice;
    }

    const paymentIntent = await stripe.paymentIntents.create({
      amount,
      currency: "usd",
      automatic_payment_methods: {
        enabled: true,
      },
      metadata: {
        licenseType,
        additionalDevices: additionalDevices.toString(),
        email,
        deviceId: deviceId || "",
        flow_type: "embedded",
      },
    });

    res.send({
      clientSecret: paymentIntent.client_secret,
      amount,
      licenseType,
      paymentIntentId: paymentIntent.id,
    });
  } catch (error) {
    console.error("Error creating payment intent:", error);
    res.status(500).json({ error: "Payment setup failed" });
  }
});

// Create Stripe Checkout Session for redirect-based payments
router.post("/create-checkout-session", requestLoggingMiddleware(EndpointPrefix.PAYMENT_CREATE_CHECKOUT), async (req, res) => {
  try {
    const context = extractRequestContext(req);
    Logger.info(EndpointPrefix.PAYMENT_CREATE_CHECKOUT, "Processing checkout session creation", context);

    const { licenseType, additionalDevices = 0, email, deviceId, successUrl, cancelUrl } = req.body;

    Logger.info(EndpointPrefix.PAYMENT_CREATE_CHECKOUT, `Checkout session requested: ${licenseType}`, {
      ...context,
      body: { licenseType, additionalDevices, email, deviceId: deviceId ? '[PROVIDED]' : '[NOT_PROVIDED]', successUrl, cancelUrl }
    });

    if (!["standard", "extended"].includes(licenseType)) {
      Logger.error(EndpointPrefix.PAYMENT_CREATE_CHECKOUT, `Invalid license type: ${licenseType}`, context);
      return res.status(400).json({ error: "Invalid license type" });
    }

    if (!successUrl || !cancelUrl) {
      return res.status(400).json({ error: "Success and cancel URLs are required" });
    }

    let amount = PRICING[licenseType];

    // Add cost for additional devices
    if (additionalDevices > 0) {
      amount += additionalDevices * PRICING.additionalDevice;
    }

    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [
        {
          price_data: {
            currency: 'usd',
            product_data: {
              name: `${licenseType.charAt(0).toUpperCase() + licenseType.slice(1)} License`,
              description: `License for ${PRICING[licenseType] === 2999 ? '2' : '5'} devices${additionalDevices > 0 ? ` + ${additionalDevices} additional devices` : ''}`,
            },
            unit_amount: amount,
          },
          quantity: 1,
        },
      ],
      mode: 'payment',
      success_url: `${successUrl}?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: cancelUrl,
      customer_email: email,
      metadata: {
        licenseType,
        additionalDevices: additionalDevices.toString(),
        email,
        deviceId: deviceId || "",
        flow_type: "checkout",
      },
    });

    res.json({
      sessionId: session.id,
      url: session.url,
      amount,
      licenseType,
    });
  } catch (error) {
    console.error("Error creating checkout session:", error);
    res.status(500).json({ error: "Checkout session creation failed" });
  }
});

// Create payment intent for license upgrade
router.post("/create-upgrade-payment-intent", requestLoggingMiddleware(EndpointPrefix.PAYMENT_CREATE_INTENT), async (req, res) => {
  try {
    const context = extractRequestContext(req);
    Logger.info(EndpointPrefix.PAYMENT_CREATE_INTENT, "Processing upgrade payment intent creation", context);

    const { licenseKey, additionalDevices } = req.body;

    Logger.info(EndpointPrefix.PAYMENT_CREATE_INTENT, "Upgrade payment intent requested", {
      ...context,
      body: {
        licenseKey: licenseKey ? `${licenseKey.substring(0, 4)}...${licenseKey.substring(licenseKey.length - 4)}` : '[NOT_PROVIDED]',
        additionalDevices
      }
    });

    if (!licenseKey || !additionalDevices || additionalDevices <= 0) {
      Logger.error(EndpointPrefix.PAYMENT_CREATE_INTENT, "Invalid upgrade request parameters", context);
      return res.status(400).json({ error: "Invalid upgrade request" });
    }

    // Verify license exists
    const license = await prisma.license.findUnique({
      where: { licenseKey },
    });

    if (!license) {
      return res.status(404).json({ error: "License not found" });
    }

    const amount = additionalDevices * PRICING.additionalDevice;

    const paymentIntent = await stripe.paymentIntents.create({
      amount,
      currency: "usd",
      automatic_payment_methods: {
        enabled: true,
      },
      metadata: {
        licenseKey,
        additionalDevices: additionalDevices.toString(),
        upgradeType: "additional_devices",
      },
    });

    res.send({
      clientSecret: paymentIntent.client_secret,
      amount,
      additionalDevices,
    });
  } catch (error) {
    console.error("Error creating upgrade payment intent:", error);
    res.status(500).json({ error: "Upgrade payment setup failed" });
  }
});

// Webhook to handle successful payments
router.post(
  "/webhook",
  express.raw({ type: "application/json" }),
  requestLoggingMiddleware(EndpointPrefix.PAYMENT_WEBHOOK),
  async (req, res) => {
    const context = extractRequestContext(req);
    Logger.info(EndpointPrefix.PAYMENT_WEBHOOK, "Processing Stripe webhook", {
      ...context,
      body: '[RAW_STRIPE_DATA]'
    });

    const sig = req.headers["stripe-signature"] as string;
    let event: Stripe.Event;

    try {
      event = stripe.webhooks.constructEvent(
        req.body,
        sig,
        process.env.STRIPE_WEBHOOK_SECRET as string
      );

      Logger.info(EndpointPrefix.PAYMENT_WEBHOOK, `Webhook event received: ${event.type}`, {
        ...context,
        body: { eventType: event.type, eventId: event.id }
      });
    } catch (err) {
      const errorMessage = (err as Error).message;
      Logger.error(EndpointPrefix.PAYMENT_WEBHOOK, `Webhook signature verification failed: ${errorMessage}`, context);
      return res.status(400).send(`Webhook Error: ${errorMessage}`);
    }

    // Handle the event
    switch (event.type) {
      case "payment_intent.succeeded":
        await handlePaymentSuccess(event.data.object);
        break;

      case "payment_intent.payment_failed":
        await handlePaymentFailure(event.data.object);
        break;

      case "checkout.session.completed":
        await handleCheckoutSuccess(event.data.object);
        break;

      default:
        console.log(`Unhandled event type ${event.type}`);
    }

    res.json({ received: true });
  }
);

async function handlePaymentSuccess(paymentIntent: Stripe.PaymentIntent) {
  try {
    const { licenseType, additionalDevices, email, licenseKey, upgradeType, deviceId, flow_type } =
      paymentIntent.metadata;

    if (upgradeType === "additional_devices") {
      // Handle license upgrade
      await prisma.license.update({
        where: { licenseKey },
        data: {
          maxDevices: {
            increment: parseInt(additionalDevices),
          },
          upgradePaymentIntentId: {
            push: paymentIntent.id,
          },
        },
      });

      console.log(
        `License ${licenseKey} upgraded with ${additionalDevices} additional devices`
      );
    } else if (flow_type === "embedded") {
      // For embedded flow, just log - license creation happens via separate API call
      console.log(
        `Payment succeeded for new ${licenseType} license for ${email} (embedded flow)`
      );
    } else {
      // For other flows, auto-create license
      await createLicenseFromPayment({
        email,
        licenseType,
        deviceId,
        stripePaymentIntentId: paymentIntent.id,
      });
    }

    // Log the successful payment
    await prisma.auditLog.create({
      data: {
        action: "LICENSE_CREATED",
        details: {
          paymentIntentId: paymentIntent.id,
          licenseType,
          email,
          amount: paymentIntent.amount,
          flow_type,
        },
      },
    });
  } catch (error) {
    console.error("Error handling payment success:", error);
  }
}

async function handleCheckoutSuccess(session: Stripe.Checkout.Session) {
  try {
    const { licenseType, email, deviceId } = session.metadata || {};

    if (!email || !licenseType) {
      console.error("Missing required metadata in checkout session");
      return;
    }

    // Auto-create license for checkout flow
    await createLicenseFromPayment({
      email,
      licenseType,
      deviceId,
      stripePaymentIntentId: session.payment_intent as string,
    });

    console.log(`Checkout completed for ${licenseType} license for ${email}`);
  } catch (error) {
    console.error("Error handling checkout success:", error);
  }
}

// Helper function to create license from successful payment
async function createLicenseFromPayment({
  email,
  licenseType,
  deviceId,
  stripePaymentIntentId,
}: {
  email: string;
  licenseType: string;
  deviceId?: string;
  stripePaymentIntentId: string;
}) {
  const { hashDeviceId, generateLicenseKey } = await import("@/utils");
  const { sendLicenseEmail } = await import("@/templates");

  // Check if license already exists for this payment
  const existingLicense = await prisma.license.findFirst({
    where: { stripePaymentIntentId },
  });

  if (existingLicense) {
    console.log(`License already exists for payment ${stripePaymentIntentId}`);
    return existingLicense;
  }

  // Generate license key
  const licenseKey = generateLicenseKey();

  // Set license parameters based on type
  let maxDevices: number;
  let expiresAt: Date | null = null;

  switch (licenseType) {
    case "trial":
      maxDevices = 1;
      expiresAt = new Date(Date.now() + 14 * 24 * 60 * 60 * 1000); // 14 days
      break;
    case "standard":
      maxDevices = 2;
      expiresAt = null; // Lifetime license
      break;
    case "extended":
      maxDevices = 5;
      expiresAt = null; // Lifetime license
      break;
    default:
      throw new Error(`Invalid license type: ${licenseType}`);
  }

  // Create license
  const license = await prisma.license.create({
    data: {
      licenseKey,
      email: email.toLowerCase(),
      licenseType,
      maxDevices,
      expiresAt,
      stripePaymentIntentId,
    },
  });

  // Register the initial device if provided
  if (deviceId) {
    const { hash: deviceHash, salt } = hashDeviceId(deviceId);

    await prisma.device.create({
      data: {
        licenseId: license.id,
        deviceHash,
        salt,
        firstSeen: new Date(),
        lastSeen: new Date(),
      },
    });
  }

  // Send license email
  await sendLicenseEmail(email, licenseKey, licenseType, expiresAt);

  console.log(`Auto-created license ${licenseKey} for ${email}`);
  return license;
}

async function handlePaymentFailure(paymentIntent: Stripe.PaymentIntent) {
  try {
    const { email, licenseType } = paymentIntent.metadata;

    // Log the failed payment
    await prisma.auditLog.create({
      data: {
        action: "SUSPICIOUS_ACTIVITY",
        details: {
          paymentIntentId: paymentIntent.id,
          licenseType,
          email,
          error: "Payment failed",
          amount: paymentIntent.amount,
        },
      },
    });

    console.log(`Payment failed for ${licenseType} license for ${email}`);
  } catch (error) {
    console.error("Error handling payment failure:", error);
  }
}

// Get pricing information
router.get("/pricing", requestLoggingMiddleware(EndpointPrefix.PAYMENT_PRICING), (req, res) => {
  const context = extractRequestContext(req);
  Logger.info(EndpointPrefix.PAYMENT_PRICING, "Pricing information requested", context);

  res.json({
    trial: {
      price: 0,
      maxDevices: 1,
      duration: "14 days",
    },
    standard: {
      price: PRICING.standard,
      maxDevices: 2,
      duration: "Lifetime",
    },
    extended: {
      price: PRICING.extended,
      maxDevices: 5,
      duration: "Lifetime",
    },
    additionalDevice: {
      price: PRICING.additionalDevice,
      description: "Per additional device",
    },
  });
});

// Get checkout session status (for redirect flow)
router.get("/checkout-session/:sessionId", requestLoggingMiddleware(EndpointPrefix.PAYMENT_STATUS), async (req, res) => {
  try {
    const context = extractRequestContext(req);
    Logger.info(EndpointPrefix.PAYMENT_STATUS, "Processing checkout session status request", context);

    const { sessionId } = req.params;

    Logger.info(EndpointPrefix.PAYMENT_STATUS, "Checkout session status requested", {
      ...context,
      params: { sessionId: sessionId ? '[PROVIDED]' : '[NOT_PROVIDED]' }
    });

    const session = await stripe.checkout.sessions.retrieve(sessionId);

    if (!session) {
      return res.status(404).json({ error: "Session not found" });
    }

    // Check if license was created for this session
    let license = null;
    if (session.payment_intent) {
      license = await prisma.license.findFirst({
        where: { stripePaymentIntentId: session.payment_intent as string },
        select: {
          licenseKey: true,
          licenseType: true,
          maxDevices: true,
          expiresAt: true,
          email: true,
        },
      });
    }

    res.json({
      sessionId: session.id,
      paymentStatus: session.payment_status,
      customerEmail: session.customer_email,
      amountTotal: session.amount_total,
      currency: session.currency,
      license: license || null,
      metadata: session.metadata,
    });
  } catch (error) {
    console.error("Error retrieving checkout session:", error);
    res.status(500).json({ error: "Failed to retrieve session" });
  }
});

// Get payment intent status (for embedded flow)
router.get("/payment-intent/:paymentIntentId", requestLoggingMiddleware(EndpointPrefix.PAYMENT_STATUS), async (req, res) => {
  try {
    const context = extractRequestContext(req);
    Logger.info(EndpointPrefix.PAYMENT_STATUS, "Processing payment intent status request", context);

    const { paymentIntentId } = req.params;

    Logger.info(EndpointPrefix.PAYMENT_STATUS, "Payment intent status requested", {
      ...context,
      params: { paymentIntentId: paymentIntentId ? '[PROVIDED]' : '[NOT_PROVIDED]' }
    });

    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);

    if (!paymentIntent) {
      return res.status(404).json({ error: "Payment intent not found" });
    }

    // Check if license was created for this payment
    let license = null;
    if (paymentIntent.status === "succeeded") {
      license = await prisma.license.findFirst({
        where: { stripePaymentIntentId: paymentIntent.id },
        select: {
          licenseKey: true,
          licenseType: true,
          maxDevices: true,
          expiresAt: true,
          email: true,
        },
      });
    }

    res.json({
      paymentIntentId: paymentIntent.id,
      status: paymentIntent.status,
      amount: paymentIntent.amount,
      currency: paymentIntent.currency,
      license: license || null,
      metadata: paymentIntent.metadata,
    });
  } catch (error) {
    console.error("Error retrieving payment intent:", error);
    res.status(500).json({ error: "Failed to retrieve payment intent" });
  }
});

export default router;
