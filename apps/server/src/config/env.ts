import { z } from "zod";

/**
 * Environment variable validation schema
 * This ensures all required environment variables are present and valid
 */
const envSchema = z.object({
	// Server Configuration
	NODE_ENV: z
		.enum(["development", "production", "test"])
		.default("development"),
	PORT: z
		.string()
		.regex(/^\d+$/, "PORT must be a valid number")
		.transform((val) => parseInt(val, 10))
		.refine((val) => val > 0 && val < 65536, "PORT must be between 1 and 65535")
		.default(3000),

	// Database Configuration
	DATABASE_URL: z
		.string()
		.min(1, "DATABASE_URL is required")
		.refine((url) => {
			try {
				const parsedUrl = new URL(url);
				return (
					parsedUrl.protocol === "postgresql:" ||
					parsedUrl.protocol === "postgres:"
				);
			} catch {
				return false;
			}
		}, "DATABASE_URL must be a valid PostgreSQL connection string"),

	// CORS Configuration
	CORS_ORIGIN: z
		.string()
		.min(1, "CORS_ORIGIN cannot be empty")
		.refine((origin) => {
			// Allow localhost for development
			if (origin.includes("localhost") || origin.includes("127.0.0.1")) {
				return true;
			}
			// Validate as URL for production
			try {
				new URL(origin);
				return true;
			} catch {
				return false;
			}
		}, "CORS_ORIGIN must be a valid URL or localhost"),

	// JWT Configuration
	JWT_SECRET: z
		.string()
		.min(32, "JWT_SECRET must be at least 32 characters long for security")
		.refine(
			(secret) => !/^(test|dev|secret|password|123)/.test(secret.toLowerCase()),
			"JWT_SECRET should not use common weak patterns",
		),

	// Stripe Configuration
	STRIPE_SECRET_KEY: z
		.string()
		.regex(
			/^sk_(test_|live_)[a-zA-Z0-9]{20,}$/,
			"STRIPE_SECRET_KEY must be a valid Stripe secret key (sk_test_* or sk_live_*)",
		),
	STRIPE_PUBLIC_KEY: z
		.string()
		.regex(
			/^pk_(test_|live_)[a-zA-Z0-9]{20,}$/,
			"STRIPE_PUBLIC_KEY must be a valid Stripe public key (pk_test_* or pk_live_*)",
		),
	STRIPE_WEBHOOK_SECRET: z
		.string()
		.regex(
			/^whsec_[a-zA-Z0-9]{32,}$/,
			"STRIPE_WEBHOOK_SECRET must be a valid Stripe webhook secret (whsec_*)",
		),

	// Email Configuration (SMTP)
	SMTP_HOST: z
		.string()
		.min(1, "SMTP_HOST is required")
		.refine((host) => {
			// Basic hostname validation
			return /^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(host);
		}, "SMTP_HOST must be a valid hostname"),
	SMTP_PORT: z
		.string()
		.regex(/^\d+$/, "SMTP_PORT must be a valid number")
		.transform((val) => parseInt(val, 10))
		.refine(
			(port) => [25, 465, 587, 2525].includes(port),
			"SMTP_PORT must be a valid SMTP port (25, 465, 587, or 2525)",
		),
	SMTP_USER: z.string().min(1, "SMTP_USER is required"),
	SMTP_PASS: z
		.string()
		.min(1, "SMTP_PASS is required")
		.min(8, "SMTP_PASS should be at least 8 characters long"),
	FROM_EMAIL: z
		.string()
		.refine(
			(email) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email),
			"FROM_EMAIL must be a valid email address",
		),

	// Security Configuration (Optional)
	BLOCKED_IPS: z
		.string()
		.optional()
		.transform((val) => val?.split(",").map((ip) => ip.trim()) || [])
		.refine((ips) => {
			return ips.every((ip) => {
				// Basic IP validation (IPv4)
				return (
					/^(\d{1,3}\.){3}\d{1,3}$/.test(ip) &&
					ip.split(".").every((octet) => parseInt(octet) <= 255)
				);
			});
		}, "BLOCKED_IPS must be a comma-separated list of valid IPv4 addresses"),
	REQUEST_SIGNING_SECRET: z
		.string()
		.min(32, "REQUEST_SIGNING_SECRET must be at least 32 characters long")
		.optional(),

	// Rate Limiting Configuration (Optional)
	RATE_LIMIT_WINDOW_MS: z
		.string()
		.regex(/^\d+$/, "RATE_LIMIT_WINDOW_MS must be a valid number")
		.transform((val) => parseInt(val, 10))
		.default(900000), // 15 minutes
	RATE_LIMIT_MAX_REQUESTS: z
		.string()
		.regex(/^\d+$/, "RATE_LIMIT_MAX_REQUESTS must be a valid number")
		.transform((val) => parseInt(val, 10))
		.default(100),

	// Logging Configuration (Optional)
	LOG_LEVEL: z.enum(["error", "warn", "info", "debug"]).default("info"),
});

/**
 * Validate and parse environment variables
 * This function should be called at application startup
 */
export function validateEnv() {
	try {
		const env = envSchema.parse(process.env);

		// Additional validation for production environment
		if (env.NODE_ENV === "production") {
			// Ensure secure configurations in production
			if (env.JWT_SECRET.length < 64) {
				console.warn(
					"WARNING: JWT_SECRET should be at least 64 characters in production",
				);
			}

			if (env.CORS_ORIGIN.includes("localhost")) {
				throw new Error(
					"CORS_ORIGIN should not include localhost in production",
				);
			}

			// Allow test keys if ALLOW_TEST_STRIPE_KEYS is set to "true"
			if (
				env.STRIPE_SECRET_KEY.startsWith("sk_test_") &&
				process.env.ALLOW_TEST_STRIPE_KEYS !== "true"
			) {
				throw new Error(
					"Cannot use test Stripe keys in production (set ALLOW_TEST_STRIPE_KEYS=true to override)",
				);
			}
		}

		console.log("✅ Environment variables validated successfully");
		return env;
	} catch (error) {
		if (error instanceof z.ZodError) {
			console.error("❌ Environment variable validation failed:");
			error.issues.forEach((err) => {
				console.error(`  - ${err.path.join(".")}: ${err.message}`);
			});
		} else {
			console.error("❌ Environment validation error:", error);
		}

		process.exit(1);
	}
}

/**
 * Type-safe environment variables
 * Use this instead of process.env for type safety
 */
export type Env = z.infer<typeof envSchema>;

// Validate environment on module load in production
if (process.env.NODE_ENV !== "test") {
	validateEnv();
}

export { envSchema };
