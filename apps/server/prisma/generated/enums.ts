
/* !!! This is code generated by <PERSON>rism<PERSON>. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
* This file exports all enum related types from the schema.
*
* 🟢 You can import this file directly.
*/
export const LicenseType = {
  trial: 'trial',
  standard: 'standard',
  extended: 'extended'
} as const

export type LicenseType = (typeof LicenseType)[keyof typeof LicenseType]


export const AuditAction = {
  LICENSE_CREATED: 'LICENSE_CREATED',
  LICENSE_VALIDATED: 'LICENSE_VALIDATED',
  LICENSE_EXPIRED: 'LICENSE_EXPIRED',
  DEVICE_ADDED: 'DEVICE_ADDED',
  DEVICE_REMOVED: 'DEVICE_REMOVED',
  SUSPICIOUS_ACTIVITY: 'SUSPICIOUS_ACTIVITY',
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  TRIAL_RESTRICTION_VIOLATED: 'TRIAL_RESTRICTION_VIOLATED',
  TRIAL_ABUSE_DETECTED: 'TRIAL_ABUSE_DETECTED'
} as const

export type AuditAction = (typeof AuditAction)[keyof typeof AuditAction]
