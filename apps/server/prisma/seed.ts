import "dotenv/config";
import { createId } from "@paralleldrive/cuid2";
import bcrypt from "bcryptjs";
import { PrismaClient } from "./generated/client";

const prisma = new PrismaClient();

/**
 * Generate a hashed device ID with salt for testing
 */
function generateHashedDevice(deviceId: string): {
  deviceHash: string;
  salt: string;
} {
  const salt = bcrypt.genSaltSync(10);
  const deviceHash = bcrypt.hashSync(deviceId, salt);
  return { deviceHash, salt };
}

/**
 * Generate sample license data
 */
async function seedLicenses() {
  console.log("🌱 Seeding licenses...");

  // Sample trial licenses
  const trialLicenses = [
    {
      licenseKey: createId(),
      email: "<EMAIL>",
      licenseType: "trial" as const,
      maxDevices: 1,
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
      stripePaymentIntentId: null,
    },
    {
      licenseKey: createId(),
      email: "<EMAIL>",
      licenseType: "trial" as const,
      maxDevices: 1,
      expiresAt: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 14 days from now
      stripePaymentIntentId: null,
    },
  ];

  // Sample standard licenses (lifetime - no expiration)
  const standardLicenses = [
    {
      licenseKey: createId(),
      email: "<EMAIL>",
      licenseType: "standard" as const,
      maxDevices: 2,
      expiresAt: null, // Lifetime license
      stripePaymentIntentId: "pi_test_standard_1",
    },
    {
      licenseKey: createId(),
      email: "<EMAIL>",
      licenseType: "standard" as const,
      maxDevices: 2,
      expiresAt: null, // Lifetime license
      stripePaymentIntentId: "pi_test_standard_2",
    },
  ];

  // Sample extended licenses (lifetime - no expiration)
  const extendedLicenses = [
    {
      licenseKey: createId(),
      email: "<EMAIL>",
      licenseType: "extended" as const,
      maxDevices: 5,
      expiresAt: null, // Lifetime license
      stripePaymentIntentId: "pi_test_extended_1",
      upgradePaymentIntentId: ["pi_test_upgrade_1"],
    },
    {
      licenseKey: createId(),
      email: "<EMAIL>",
      licenseType: "extended" as const,
      maxDevices: 10,
      expiresAt: null, // Lifetime license
      stripePaymentIntentId: "pi_test_extended_2",
      upgradePaymentIntentId: ["pi_test_upgrade_2", "pi_test_upgrade_3"],
    },
  ];

  // Sample expired license for testing
  const expiredLicense = {
    licenseKey: createId(),
    email: "<EMAIL>",
    licenseType: "trial" as const,
    maxDevices: 1,
    expiresAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
    stripePaymentIntentId: null,
  };

  const allLicenses = [
    ...trialLicenses,
    ...standardLicenses,
    ...extendedLicenses,
    expiredLicense,
  ];

  // Create licenses
  const createdLicenses = [];
  for (const licenseData of allLicenses) {
    const license = await prisma.license.create({
      data: licenseData,
    });
    createdLicenses.push(license);
    console.log(
      `✅ Created ${license.licenseType} license: ${license.licenseKey} for ${license.email}`
    );
  }

  return createdLicenses;
}

/**
 * Generate sample device data
 */
async function seedDevices(
  licenses: Awaited<ReturnType<typeof prisma.license.create>>[]
) {
  console.log("🖥️  Seeding devices...");

  const sampleDevices = [
    // Devices for trial licenses
    {
      licenseId: licenses[0].id,
      deviceId: "a1b2c3d4e5f6789012345678901234567890abcd",
      appVersion: "1.0.0",
      isActive: true,
    },
    {
      licenseId: licenses[1].id,
      deviceId: "b2c3d4e5f6789012345678901234567890abcdef",
      appVersion: "1.0.1",
      isActive: true,
    },
    // Devices for standard licenses (multiple devices)
    {
      licenseId: licenses[2].id,
      deviceId: "c3d4e5f6789012345678901234567890abcdef12",
      appVersion: "1.1.0",
      isActive: true,
    },
    {
      licenseId: licenses[2].id,
      deviceId: "d4e5f6789012345678901234567890abcdef1234",
      appVersion: "1.1.0",
      isActive: true,
    },
    {
      licenseId: licenses[3].id,
      deviceId: "e5f6789012345678901234567890abcdef123456",
      appVersion: "1.0.5",
      isActive: true,
    },
    // Devices for extended licenses
    {
      licenseId: licenses[4].id,
      deviceId: "f6789012345678901234567890abcdef12345678",
      appVersion: "1.2.0",
      isActive: true,
    },
    {
      licenseId: licenses[4].id,
      deviceId: "6789012345678901234567890abcdef123456789",
      appVersion: "1.2.0",
      isActive: false, // Inactive device
    },
    {
      licenseId: licenses[5].id,
      deviceId: "789012345678901234567890abcdef1234567890",
      appVersion: "1.2.1",
      isActive: true,
    },
    // Device for expired license
    {
      licenseId: licenses[6].id,
      deviceId: "89012345678901234567890abcdef12345678901",
      appVersion: "0.9.0",
      isActive: false,
    },
  ];

  const createdDevices = [];
  for (const deviceData of sampleDevices) {
    const { deviceHash, salt } = generateHashedDevice(deviceData.deviceId);

    const device = await prisma.device.create({
      data: {
        licenseId: deviceData.licenseId,
        deviceHash,
        salt,
        appVersion: deviceData.appVersion,
        isActive: deviceData.isActive,
        lastSeen: new Date(),
      },
    });
    createdDevices.push(device);
    console.log(
      `✅ Created device for license ${deviceData.licenseId}: ${device.id}`
    );
  }

  return createdDevices;
}

/**
 * Generate sample audit log data
 */
async function seedAuditLogs(
  licenses: Awaited<ReturnType<typeof prisma.license.create>>[],
  devices: Awaited<ReturnType<typeof prisma.device.create>>[]
) {
  console.log("📋 Seeding audit logs...");

  const auditLogs = [
    // License creation logs
    ...licenses.map((license) => ({
      action: "LICENSE_CREATED" as const,
      licenseKey: license.licenseKey,
      ipAddress: "*************",
      userAgent: "SnapBack/1.0.0 (macOS)",
      details: {
        licenseType: license.licenseType,
        email: license.email,
        maxDevices: license.maxDevices,
      },
      createdAt: license.createdAt,
    })),
    // Device addition logs
    ...devices.slice(0, 5).map((device, index) => ({
      action: "DEVICE_ADDED" as const,
      licenseKey: licenses.find((l) => l.id === device.licenseId)?.licenseKey,
      deviceHash: device.deviceHash,
      ipAddress: `192.168.1.${101 + index}`,
      userAgent: "SnapBack/1.0.0 (macOS)",
      details: {
        appVersion: device.appVersion,
        deviceId: device.id,
      },
      createdAt: device.firstSeen,
    })),
    // License validation logs
    {
      action: "LICENSE_VALIDATED" as const,
      licenseKey: licenses[0].licenseKey,
      deviceHash: devices[0].deviceHash,
      ipAddress: "*************",
      userAgent: "SnapBack/1.0.0 (macOS)",
      details: {
        validationResult: "success",
        deviceCount: 1,
      },
    },
    {
      action: "LICENSE_EXPIRED" as const,
      licenseKey: licenses[6].licenseKey, // Expired license
      deviceHash: devices[8].deviceHash,
      ipAddress: "*************",
      userAgent: "SnapBack/0.9.0 (macOS)",
      details: {
        expirationDate: licenses[6].expiresAt,
        attemptedAction: "validation",
      },
    },
    // Suspicious activity log
    {
      action: "SUSPICIOUS_ACTIVITY" as const,
      licenseKey: licenses[2].licenseKey,
      ipAddress: "*********",
      userAgent: "curl/7.68.0",
      details: {
        reason: "unusual_user_agent",
        attemptedAction: "license_validation",
        blocked: true,
      },
    },
    // Rate limit exceeded log
    {
      action: "RATE_LIMIT_EXCEEDED" as const,
      licenseKey: licenses[1].licenseKey,
      ipAddress: "************",
      userAgent: "SnapBack/1.0.1 (macOS)",
      details: {
        endpoint: "/api/license/validate",
        requestCount: 15,
        windowMs: 900000,
      },
    },
  ];

  const createdAuditLogs = [];
  for (const logData of auditLogs) {
    const auditLog = await prisma.auditLog.create({
      data: logData,
    });
    createdAuditLogs.push(auditLog);
  }

  console.log(`✅ Created ${createdAuditLogs.length} audit log entries`);
  return createdAuditLogs;
}

/**
 * Main seed function
 */
async function main() {
  console.log("🌱 Starting database seeding...");

  try {
    // Clear existing data (optional - remove if you want to preserve existing data)
    console.log("🧹 Cleaning existing data...");
    await prisma.auditLog.deleteMany();
    await prisma.device.deleteMany();
    await prisma.license.deleteMany();

    // Seed data
    const licenses = await seedLicenses();
    const devices = await seedDevices(licenses);
    const auditLogs = await seedAuditLogs(licenses, devices);

    console.log("✅ Database seeding completed successfully!");
    console.log(`📊 Summary:`);
    console.log(`   - ${licenses.length} licenses created`);
    console.log(`   - ${devices.length} devices created`);
    console.log(`   - ${auditLogs.length} audit log entries created`);
  } catch (error) {
    console.error("❌ Error during seeding:", error);
    throw error;
  }
}

// Execute the seed function
main()
  .catch((e) => {
    console.error("❌ Seeding failed:", e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
