generator client {
  provider     = "prisma-client"
  output       = "../generated"
  moduleFormat = "esm"
}

datasource db {
  provider = "postgres"
  url      = env("DATABASE_URL")
}

model License {
  id          String      @id @default(cuid())
  licenseKey  String      @unique
  email       String
  licenseType LicenseType
  maxDevices  Int         @default(2)
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  expiresAt   DateTime?

  // Payment tracking
  stripePaymentIntentId  String?
  upgradePaymentIntentId String[] @default([])

  // Relationships
  devices Device[]

  @@index([email])
  @@index([licenseKey])
  @@index([expiresAt])
  @@map("licenses")
}

model Device {
  id         String   @id @default(cuid())
  licenseId  String
  deviceHash String // Hashed device ID for security
  salt       String // Salt used for hashing
  firstSeen  DateTime @default(now())
  lastSeen   DateTime @default(now())
  appVersion String?
  isActive   Boolean  @default(true)

  // Relationships
  license License @relation(fields: [licenseId], references: [id], onDelete: Cascade)

  @@unique([licenseId, deviceHash])
  @@index([licenseId])
  @@index([deviceHash])
  @@map("devices")
}

model AuditLog {
  id         String      @id @default(cuid())
  action     AuditAction
  licenseKey String?
  deviceHash String?
  ipAddress  String?
  userAgent  String?
  details    Json?
  createdAt  DateTime    @default(now())

  @@index([licenseKey])
  @@index([createdAt])
  @@map("audit_logs")
}

enum LicenseType {
  trial
  standard
  extended
}

enum AuditAction {
  LICENSE_CREATED
  LICENSE_VALIDATED
  LICENSE_EXPIRED
  DEVICE_ADDED
  DEVICE_REMOVED
  SUSPICIOUS_ACTIVITY
  RATE_LIMIT_EXCEEDED
  TRIAL_RESTRICTION_VIOLATED
  TRIAL_ABUSE_DETECTED
}
