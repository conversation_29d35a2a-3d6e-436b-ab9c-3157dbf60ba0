# Database Seed Data

This document describes the seed data that gets created when running the database seed script.

## Running the Seed Script

```bash
# From the root directory
pnpm db:seed

# Or from the server directory
cd apps/server
pnpm db:seed

# To reset the database and reseed
pnpm db:reset
```

## Seed Data Overview

### 📄 Licenses (7 total)

#### Trial Licenses (2)
- **<EMAIL>**
  - License Type: `trial`
  - Max Devices: 1
  - Expires: 7 days from seed date
  - Payment: None (trial)

- **<EMAIL>**
  - License Type: `trial`
  - Max Devices: 1
  - Expires: 14 days from seed date
  - Payment: None (trial)

#### Standard Licenses (2)
- **<EMAIL>**
  - License Type: `standard`
  - Max Devices: 2
  - Expires: 1 year from seed date
  - Payment: `pi_test_standard_1`

- **<EMAIL>**
  - License Type: `standard`
  - Max Devices: 2
  - Expires: 1 year from seed date
  - Payment: `pi_test_standard_2`

#### Extended Licenses (2)
- **<EMAIL>**
  - License Type: `extended`
  - Max Devices: 5
  - Expires: 1 year from seed date
  - Payment: `pi_test_extended_1`
  - Upgrades: `["pi_test_upgrade_1"]`

- **<EMAIL>**
  - License Type: `extended`
  - Max Devices: 10
  - Expires: 2 years from seed date
  - Payment: `pi_test_extended_2`
  - Upgrades: `["pi_test_upgrade_2", "pi_test_upgrade_3"]`

#### Expired License (1)
- **<EMAIL>**
  - License Type: `trial`
  - Max Devices: 1
  - Expires: 7 days ago (expired)
  - Payment: None (trial)

### 🖥️ Devices (9 total)

Each license has associated devices with realistic device IDs and app versions:

- **Trial licenses**: 1 device each (within limits)
- **Standard licenses**: 1-2 devices each (within limits)
- **Extended licenses**: 1-2 devices each (well under limits)
- **Expired license**: 1 inactive device

Device features:
- Hashed device IDs with bcrypt for security
- Various app versions (0.9.0 to 1.2.1)
- Mix of active and inactive devices
- Realistic timestamps

### 📋 Audit Logs (15+ entries)

Comprehensive audit trail including:

#### License Events
- `LICENSE_CREATED` - For each license creation
- `LICENSE_VALIDATED` - Successful license validations
- `LICENSE_EXPIRED` - Expired license access attempts

#### Device Events
- `DEVICE_ADDED` - When devices are registered

#### Security Events
- `SUSPICIOUS_ACTIVITY` - Unusual user agent detection
- `RATE_LIMIT_EXCEEDED` - Rate limiting triggers

Each audit log includes:
- IP addresses (realistic test IPs)
- User agents (mix of legitimate and suspicious)
- Detailed metadata for debugging
- Proper timestamps

## Test Scenarios Covered

### ✅ Valid License Testing
- Active trial licenses (short and medium term)
- Active standard licenses with multiple devices
- Active extended licenses with upgrade history

### ❌ Error Condition Testing
- Expired license validation
- Rate limiting scenarios
- Suspicious activity detection

### 🔒 Security Testing
- Device fingerprinting and hashing
- Audit trail completeness
- IP-based tracking

### 📊 Business Logic Testing
- Device limits per license type
- License upgrade workflows
- Payment intent tracking

## Sample API Testing

With this seed data, you can test:

```bash
# Validate an active license
curl -X POST http://localhost:3000/api/license/validate \
  -H "Content-Type: application/json" \
  -d '{
    "licenseKey": "[trial_license_key]",
    "deviceId": "a1b2c3d4e5f6789012345678901234567890abcd",
    "appVersion": "1.0.0"
  }'

# Try to validate an expired license
curl -X POST http://localhost:3000/api/license/validate \
  -H "Content-Type: application/json" \
  -d '{
    "licenseKey": "[expired_license_key]",
    "deviceId": "89012345678901234567890abcdef12345678901",
    "appVersion": "0.9.0"
  }'
```

## Database Cleanup

The seed script includes cleanup functionality:

```typescript
// Clears all existing data before seeding
await prisma.auditLog.deleteMany();
await prisma.device.deleteMany();
await prisma.license.deleteMany();
```

**⚠️ Warning**: The seed script will delete all existing data. Comment out the cleanup section if you want to preserve existing data.

## Customization

To modify the seed data:

1. Edit `apps/server/prisma/seed.ts`
2. Adjust license types, expiration dates, or device counts
3. Add more test scenarios as needed
4. Run `pnpm db:seed` to apply changes

## Development Workflow

1. **Initial Setup**: `pnpm db:push && pnpm db:seed`
2. **Reset During Development**: `pnpm db:reset`
3. **Add More Data**: Modify seed.ts and run `pnpm db:seed`
4. **View Data**: `pnpm db:studio`
