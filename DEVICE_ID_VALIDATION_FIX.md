# Device ID Validation Fix

## 🔍 **Problem Analysis**

The Zod validation error was caused by a mismatch between the server's expected device ID format and what the Swift client was actually sending:

### **Server Expectation**
- Pure hexadecimal string: `/^[a-fA-F0-9]+$/`
- Length: 32-128 characters
- Example: `278ff9b3bd184c2c82b1311b5af61abb` (64 chars for SHA256)

### **Client Reality**
- UUID format with hyphens: `278FF9B3-BD18-4C2C-82B1-311B5AF61ABB`
- Length: 36 characters including hyphens
- This suggests the `getHardwareUUID()` function was returning a UUID instead of being used as input to SHA256

## ✅ **Server-Side Fix Applied**

### 1. Updated Zod Schema (`apps/server/src/schemas/index.ts`)

**Before:**
```typescript
const deviceIdSchema = z
  .string()
  .regex(/^[a-fA-F0-9]+$/, {
    message: "Device ID must be a valid hexadecimal string",
  })
  .transform((val) => val.toLowerCase());
```

**After:**
```typescript
const deviceIdSchema = z
  .string()
  .refine((val) => {
    // Accept pure hexadecimal strings (SHA256 format: 64 chars)
    const hexPattern = /^[a-fA-F0-9]+$/;
    // Accept UUID format (with hyphens: 36 chars)
    const uuidPattern = /^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$/i;
    
    return hexPattern.test(val) || uuidPattern.test(val);
  }, {
    message: "Device ID must be a valid hexadecimal string or UUID format",
  })
  .transform((val) => {
    // Normalize UUIDs by removing hyphens and converting to lowercase
    return val.replace(/-/g, '').toLowerCase();
  });
```

### 2. Updated Security Middleware (`apps/server/src/middleware/security/index.ts`)

**Before:**
```typescript
function isValidDeviceIdFormat(deviceId: string): boolean {
  const hexPattern = /^[a-fA-F0-9]{32,128}$/;
  return hexPattern.test(deviceId);
}
```

**After:**
```typescript
function isValidDeviceIdFormat(deviceId: string): boolean {
  // Accept pure hexadecimal strings (SHA256 format: 64 chars, or other reasonable lengths)
  const hexPattern = /^[a-fA-F0-9]{32,128}$/;
  // Accept UUID format (with hyphens: 36 chars)
  const uuidPattern = /^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$/i;
  
  return hexPattern.test(deviceId) || uuidPattern.test(deviceId);
}
```

## 🔧 **Recommended Swift Client Fix**

The issue appears to be in the Swift implementation. Here's the corrected version:

### **Current Problematic Implementation**
```swift
static func generateDeviceID() -> String {
    // Use persistent device identifier if available
    let key = "SnapbackDeviceIdentifier"
    if let existing = UserDefaults.standard.string(forKey: key) {
        return existing
    }

    // Generate new device ID based on system information
    let systemInfo = [
        ProcessInfo.processInfo.hostName,
        ProcessInfo.processInfo.operatingSystemVersionString,
        getHardwareUUID() ?? UUID().uuidString,  // ← PROBLEM: This returns UUID format
    ].joined(separator: "-")

    let data = Data(systemInfo.utf8)
    let hash = SHA256.hash(data: data)
    let deviceId = hash.compactMap { String(format: "%02x", $0) }.joined()

    // Store for future use
    UserDefaults.standard.set(deviceId, forKey: key)
    return deviceId
}
```

### **Corrected Implementation**
```swift
import CryptoKit
import Foundation

static func generateDeviceID() -> String {
    // Use persistent device identifier if available
    let key = "SnapbackDeviceIdentifier"
    if let existing = UserDefaults.standard.string(forKey: key) {
        return existing
    }

    // Generate new device ID based on system information
    let systemInfo = [
        ProcessInfo.processInfo.hostName,
        ProcessInfo.processInfo.operatingSystemVersionString,
        getHardwareIdentifier(), // ← Use a function that returns a string, not UUID
    ].joined(separator: "-")

    let data = Data(systemInfo.utf8)
    let hash = SHA256.hash(data: data)
    let deviceId = hash.compactMap { String(format: "%02x", $0) }.joined()

    // Store for future use
    UserDefaults.standard.set(deviceId, forKey: key)
    return deviceId
}

// Helper function to get hardware identifier as string
private static func getHardwareIdentifier() -> String {
    // Option 1: Use IOKit to get hardware UUID as string
    if let hardwareUUID = getHardwareUUIDString() {
        return hardwareUUID
    }
    
    // Option 2: Fallback to generated UUID string
    return UUID().uuidString
}

// Get hardware UUID as string (not UUID object)
private static func getHardwareUUIDString() -> String? {
    let platformExpert = IOServiceGetMatchingService(kIOMasterPortDefault, IOServiceMatching("IOPlatformExpertDevice"))
    
    guard platformExpert > 0 else {
        return nil
    }
    
    defer {
        IOObjectRelease(platformExpert)
    }
    
    guard let serialNumberAsCFString = IORegistryEntryCreateCFProperty(
        platformExpert,
        kIOPlatformUUIDKey as CFString,
        kCFAllocatorDefault,
        0
    )?.takeUnretainedValue() as? CFString else {
        return nil
    }
    
    return serialNumberAsCFString as String
}
```

## 🔄 **Migration Strategy**

### **Option 1: Immediate Fix (Recommended)**
1. ✅ Server now accepts both UUID and hex formats
2. Update Swift client to generate proper SHA256 hex strings
3. Existing UUID-based device IDs will continue to work
4. New installations will use secure SHA256 format

### **Option 2: Gradual Migration**
1. ✅ Server accepts both formats (already implemented)
2. Keep current Swift implementation temporarily
3. Add migration logic to convert stored UUIDs to SHA256 format
4. Eventually deprecate UUID support

## 🧪 **Testing Results**

### ✅ **Test with UUID Format (Original Issue)**
```bash
curl -X POST http://localhost:3000/api/licenses/create \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "licenseType": "trial",
    "deviceId": "278FF9B3-BD18-4C2C-82B1-311B5AF61ABB"
  }'
```

**Result**: ✅ SUCCESS
```json
{
  "message": "License created successfully",
  "licenseKey": "AS8MDV2V8UNUF99UQU24GTWP",
  "licenseType": "trial",
  "maxDevices": 1,
  "expiresAt": "2025-08-13T00:22:18.631Z",
  "devicesUsed": 1
}
```

### ✅ **Test with Hex Format (Normalized)**
```bash
curl -X POST http://localhost:3000/api/licenses/create \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "licenseType": "trial",
    "deviceId": "278ff9b3bd184c2c82b1311b5af61abb"
  }'
```

**Result**: ✅ SUCCESS
```json
{
  "message": "License created successfully",
  "licenseKey": "QV3JVHFVHFYMXQRRYJ52G9DM",
  "licenseType": "trial",
  "maxDevices": 1,
  "expiresAt": "2025-08-13T00:21:57.106Z",
  "devicesUsed": 1
}
```

### 📋 **Server Logs Confirmation**
```
[2025-07-30T00:21:57.088Z] [INFO] [LICENSE_CREATE] License creation requested: <NAME_EMAIL>
[2025-07-30T00:21:57.105Z] [INFO] [LICENSE_CREATE] Trial license restrictions passed
[2025-07-30T00:21:57.298Z] [INFO] [LICENSE_CREATE] Request completed Status: 201
```

**Both UUID and hex formats now work successfully!**

## 📋 **Next Steps**

1. **Immediate**: Test the current fix with your existing Swift client
2. **Short-term**: Update Swift client to generate proper SHA256 device IDs
3. **Long-term**: Consider deprecating UUID format support after migration period

## 🔒 **Security Considerations**

- The server normalizes all device IDs by removing hyphens and converting to lowercase
- This ensures consistent hashing regardless of input format
- SHA256 format provides better entropy and security than UUIDs
- The migration maintains backward compatibility while improving security
