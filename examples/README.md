# Website Integration Examples

This directory contains example implementations showing how to integrate your website with the snapback license server.

## Files Overview

- `website/index.html` - Main license purchase page with both payment flows
- `website/success.html` - Success page for completed purchases
- `website/cancel.html` - Cancel page for cancelled payments
- `README.md` - This file

## Setup Instructions

### 1. Update Configuration

Edit the configuration variables in `website/index.html`:

```javascript
// Configuration
const API_BASE = 'http://localhost:3000/api'; // Update with your server URL
const STRIPE_PUBLIC_KEY = 'pk_test_your_key_here'; // Update with your Stripe public key
```

Also update the API_BASE in `success.html`:

```javascript
const API_BASE = 'http://localhost:3000/api'; // Update with your server URL
```

### 2. Stripe Configuration

1. Get your Stripe keys from the [Stripe Dashboard](https://dashboard.stripe.com/apikeys)
2. Update the `STRIPE_PUBLIC_KEY` in the HTML files
3. Make sure your server has the corresponding `STRIPE_SECRET_KEY` in the environment variables

### 3. CORS Configuration

Make sure your license server allows requests from your website domain. Update the `CORS_ORIGIN` environment variable:

```env
CORS_ORIGIN=https://yourwebsite.com
```

For local development:
```env
CORS_ORIGIN=http://localhost:8080
```

### 4. Webhook Configuration

Set up a Stripe webhook pointing to your server:

**Webhook URL:** `https://your-license-server.com/api/payments/webhook`

**Events to send:**
- `payment_intent.succeeded`
- `payment_intent.payment_failed`
- `checkout.session.completed`

## Running the Examples

### Option 1: Simple HTTP Server (Python)

```bash
cd examples/website
python3 -m http.server 8080
```

Then visit: http://localhost:8080

### Option 2: Simple HTTP Server (Node.js)

```bash
cd examples/website
npx http-server -p 8080
```

Then visit: http://localhost:8080

### Option 3: Live Server (VS Code Extension)

1. Install the "Live Server" extension in VS Code
2. Right-click on `index.html`
3. Select "Open with Live Server"

## Payment Flow Options

### 1. Embedded Stripe Elements (Blue Buttons)

- Click "Purchase" on any license
- Fill in payment details in the embedded form
- Payment is processed and license is created automatically

**Pros:**
- Full control over UI/UX
- Users stay on your website
- Custom styling possible

**Cons:**
- More complex implementation
- Requires handling payment form

### 2. Stripe Checkout Redirect (Gray Buttons)

- Click "Quick Checkout" on any license
- Redirects to Stripe-hosted payment page
- Returns to success/cancel pages after payment

**Pros:**
- Easier implementation
- Stripe handles all payment UI
- Automatic license creation
- Mobile-optimized

**Cons:**
- Less customization
- Users leave your website temporarily

## Testing

Use Stripe's test card numbers:

- **Success:** `****************`
- **Decline:** `****************`
- **3D Secure:** `****************`

## Customization

### Styling

The examples use inline CSS for simplicity. For production:

1. Move CSS to external files
2. Use your brand colors and fonts
3. Add responsive design improvements
4. Implement your design system

### Functionality

You can extend the examples with:

- User accounts and login
- License management dashboard
- Multiple device registration
- License upgrades
- Bulk purchases
- Discount codes

### Error Handling

The examples include basic error handling. For production, consider:

- More detailed error messages
- Retry mechanisms
- Logging and monitoring
- User-friendly error pages

## Security Considerations

1. **HTTPS Only:** Always use HTTPS in production
2. **API Keys:** Never expose secret keys in frontend code
3. **Validation:** Validate all inputs on the server side
4. **Rate Limiting:** The server includes rate limiting
5. **CORS:** Configure CORS properly for your domain

## Production Checklist

- [ ] Update API_BASE to production server URL
- [ ] Replace test Stripe keys with live keys
- [ ] Configure production webhook endpoints
- [ ] Set up proper CORS origins
- [ ] Test all payment flows thoroughly
- [ ] Set up monitoring and logging
- [ ] Configure SSL certificates
- [ ] Test email delivery
- [ ] Verify license creation process

## Support

If you encounter issues:

1. Check browser console for errors
2. Verify server is running and accessible
3. Check Stripe webhook logs
4. Verify environment variables are set correctly
5. Test with Stripe's test cards first

For additional help, refer to:
- [Stripe Documentation](https://stripe.com/docs)
- [Stripe Testing Guide](https://stripe.com/docs/testing)
- Main project documentation
