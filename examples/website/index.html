<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>License Purchase - Example Website</title>
    <script src="https://js.stripe.com/v3/"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .license-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .license-card {
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            transition: border-color 0.2s;
        }
        .license-card:hover {
            border-color: #0066cc;
        }
        .license-card h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .price {
            font-size: 24px;
            font-weight: bold;
            color: #0066cc;
            margin: 10px 0;
        }
        .features {
            list-style: none;
            padding: 0;
            margin: 15px 0;
        }
        .features li {
            padding: 5px 0;
            color: #666;
        }
        .btn {
            background: #0066cc;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: background-color 0.2s;
        }
        .btn:hover {
            background: #0052a3;
        }
        .btn-secondary {
            background: #6c757d;
        }
        .btn-secondary:hover {
            background: #545b62;
        }
        .form-group {
            margin: 15px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        .form-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        .payment-form {
            display: none;
            margin-top: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        #card-element {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
        }
        .error {
            color: #dc3545;
            margin-top: 10px;
        }
        .success {
            color: #28a745;
            margin-top: 10px;
        }
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Choose Your License</h1>
        <p>Select the license that best fits your needs. All licenses include lifetime updates and email support.</p>
        
        <div class="license-grid" id="license-grid">
            <!-- License options will be loaded here -->
        </div>

        <div class="form-group">
            <label for="email">Email Address:</label>
            <input type="email" id="email" placeholder="<EMAIL>" required>
        </div>

        <div class="form-group">
            <label for="device-id">Device ID (Optional):</label>
            <input type="text" id="device-id" placeholder="Leave empty to register device later">
        </div>

        <!-- Embedded Payment Form -->
        <div id="payment-form" class="payment-form">
            <h3>Payment Details</h3>
            <div id="card-element"></div>
            <div id="card-errors" class="error"></div>
            <button id="submit-payment" class="btn">Complete Payment</button>
            <button id="cancel-payment" class="btn btn-secondary">Cancel</button>
        </div>

        <div id="messages"></div>
    </div>

    <script>
        // Configuration
        const API_BASE = 'http://localhost:3000/api'; // Update with your server URL
        const STRIPE_PUBLIC_KEY = 'pk_test_your_key_here'; // Update with your Stripe public key
        
        // Initialize Stripe
        const stripe = Stripe(STRIPE_PUBLIC_KEY);
        let elements, cardElement;
        let selectedLicense = null;

        // Load pricing and render license options
        async function loadPricing() {
            try {
                const response = await fetch(`${API_BASE}/payments/pricing`);
                const pricing = await response.json();
                renderLicenseOptions(pricing);
            } catch (error) {
                showMessage('Failed to load pricing information', 'error');
            }
        }

        function renderLicenseOptions(pricing) {
            const grid = document.getElementById('license-grid');
            
            Object.entries(pricing).forEach(([type, info]) => {
                if (type === 'additionalDevice') return; // Skip additional device pricing
                
                const card = document.createElement('div');
                card.className = 'license-card';
                card.innerHTML = `
                    <h3>${type.charAt(0).toUpperCase() + type.slice(1)} License</h3>
                    <div class="price">$${(info.price / 100).toFixed(2)}</div>
                    <ul class="features">
                        <li>✓ ${info.maxDevices} Device${info.maxDevices > 1 ? 's' : ''}</li>
                        <li>✓ ${info.duration}</li>
                        <li>✓ Email Support</li>
                        <li>✓ Free Updates</li>
                    </ul>
                    <button class="btn" onclick="selectLicense('${type}', ${info.price})">
                        ${type === 'trial' ? 'Start Trial' : 'Purchase'}
                    </button>
                    <button class="btn btn-secondary" onclick="purchaseWithRedirect('${type}')">
                        Quick Checkout
                    </button>
                `;
                grid.appendChild(card);
            });
        }

        // Select license for embedded payment
        function selectLicense(type, price) {
            const email = document.getElementById('email').value;
            if (!email) {
                showMessage('Please enter your email address', 'error');
                return;
            }

            selectedLicense = { type, price };
            
            if (type === 'trial') {
                // Trial licenses don't require payment
                createTrialLicense();
            } else {
                showPaymentForm();
            }
        }

        // Create trial license directly
        async function createTrialLicense() {
            const email = document.getElementById('email').value;
            const deviceId = document.getElementById('device-id').value;

            try {
                showMessage('Creating trial license...', 'info');
                
                const response = await fetch(`${API_BASE}/licenses/create`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        email,
                        licenseType: 'trial',
                        deviceId: deviceId || undefined
                    })
                });

                const result = await response.json();
                
                if (response.ok) {
                    showMessage(`Trial license created! Check your email for the license key: ${result.licenseKey}`, 'success');
                } else {
                    showMessage(result.error || 'Failed to create trial license', 'error');
                }
            } catch (error) {
                showMessage('Failed to create trial license', 'error');
            }
        }

        // Show embedded payment form
        function showPaymentForm() {
            document.getElementById('payment-form').style.display = 'block';
            
            // Initialize Stripe Elements
            elements = stripe.elements();
            cardElement = elements.create('card');
            cardElement.mount('#card-element');

            cardElement.on('change', ({error}) => {
                const displayError = document.getElementById('card-errors');
                if (error) {
                    displayError.textContent = error.message;
                } else {
                    displayError.textContent = '';
                }
            });
        }

        // Handle embedded payment submission
        document.getElementById('submit-payment').addEventListener('click', async () => {
            if (!selectedLicense) return;

            const email = document.getElementById('email').value;
            const deviceId = document.getElementById('device-id').value;

            try {
                document.body.classList.add('loading');
                showMessage('Processing payment...', 'info');

                // Create payment intent
                const intentResponse = await fetch(`${API_BASE}/payments/create-payment-intent`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        licenseType: selectedLicense.type,
                        email,
                        deviceId: deviceId || undefined
                    })
                });

                const { clientSecret, paymentIntentId } = await intentResponse.json();

                // Confirm payment
                const {error, paymentIntent} = await stripe.confirmCardPayment(clientSecret, {
                    payment_method: {
                        card: cardElement,
                        billing_details: { email }
                    }
                });

                if (error) {
                    showMessage(error.message, 'error');
                } else if (paymentIntent.status === 'succeeded') {
                    // Create license
                    const licenseResponse = await fetch(`${API_BASE}/licenses/create`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            email,
                            licenseType: selectedLicense.type,
                            deviceId: deviceId || undefined,
                            stripePaymentIntentId: paymentIntentId
                        })
                    });

                    const license = await licenseResponse.json();
                    
                    if (licenseResponse.ok) {
                        showMessage(`Payment successful! License created: ${license.licenseKey}`, 'success');
                        hidePaymentForm();
                    } else {
                        showMessage(license.error || 'Payment succeeded but license creation failed', 'error');
                    }
                }
            } catch (error) {
                showMessage('Payment processing failed', 'error');
            } finally {
                document.body.classList.remove('loading');
            }
        });

        // Purchase with redirect (Stripe Checkout)
        async function purchaseWithRedirect(licenseType) {
            const email = document.getElementById('email').value;
            if (!email) {
                showMessage('Please enter your email address', 'error');
                return;
            }

            const deviceId = document.getElementById('device-id').value;

            try {
                showMessage('Redirecting to checkout...', 'info');
                
                const response = await fetch(`${API_BASE}/payments/create-checkout-session`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        licenseType,
                        email,
                        deviceId: deviceId || undefined,
                        successUrl: `${window.location.origin}/success.html`,
                        cancelUrl: `${window.location.origin}/cancel.html`
                    })
                });

                const { url } = await response.json();
                window.location.href = url;
            } catch (error) {
                showMessage('Failed to create checkout session', 'error');
            }
        }

        // Cancel payment
        document.getElementById('cancel-payment').addEventListener('click', hidePaymentForm);

        function hidePaymentForm() {
            document.getElementById('payment-form').style.display = 'none';
            selectedLicense = null;
            if (cardElement) {
                cardElement.destroy();
            }
        }

        function showMessage(message, type) {
            const messages = document.getElementById('messages');
            messages.innerHTML = `<div class="${type}">${message}</div>`;
            
            // Auto-clear success messages
            if (type === 'success') {
                setTimeout(() => {
                    messages.innerHTML = '';
                }, 5000);
            }
        }

        // Load pricing on page load
        loadPricing();
    </script>
</body>
</html>
