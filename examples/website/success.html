<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Successful - License Created</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .success-icon {
            font-size: 64px;
            color: #28a745;
            margin-bottom: 20px;
        }
        h1 {
            color: #28a745;
            margin-bottom: 10px;
        }
        .license-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }
        .license-key {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 18px;
            font-weight: bold;
            color: #0066cc;
            background: white;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #ddd;
            word-break: break-all;
        }
        .btn {
            background: #0066cc;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover {
            background: #0052a3;
        }
        .loading {
            color: #666;
            font-style: italic;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
        }
        .info-row {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        .info-row:last-child {
            border-bottom: none;
        }
        .info-label {
            font-weight: 500;
            color: #333;
        }
        .info-value {
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="success-icon">✅</div>
        <h1>Payment Successful!</h1>
        <p>Your license has been created and sent to your email address.</p>
        
        <div id="license-container">
            <div class="loading">Loading license information...</div>
        </div>
        
        <div style="margin-top: 30px;">
            <a href="index.html" class="btn">Purchase Another License</a>
            <button onclick="copyLicenseKey()" class="btn" id="copy-btn" style="display: none;">Copy License Key</button>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000/api'; // Update with your server URL
        let currentLicenseKey = '';

        async function loadLicenseInfo() {
            const urlParams = new URLSearchParams(window.location.search);
            const sessionId = urlParams.get('session_id');
            
            if (!sessionId) {
                showError('No session ID found in URL. Please check your payment confirmation email.');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/payments/checkout-session/${sessionId}`);
                
                if (!response.ok) {
                    throw new Error('Failed to retrieve session information');
                }
                
                const session = await response.json();
                
                if (session.paymentStatus !== 'paid') {
                    showError('Payment was not completed successfully. Please contact support.');
                    return;
                }
                
                if (!session.license) {
                    showError('License is being created. Please check your email in a few minutes or contact support if you don\'t receive it.');
                    return;
                }
                
                displayLicenseInfo(session);
                
            } catch (error) {
                console.error('Error loading license info:', error);
                showError('Failed to load license information. Please check your email or contact support.');
            }
        }

        function displayLicenseInfo(session) {
            const container = document.getElementById('license-container');
            const license = session.license;
            currentLicenseKey = license.licenseKey;
            
            const expirationText = license.expiresAt 
                ? `Expires: ${new Date(license.expiresAt).toLocaleDateString()}`
                : 'Lifetime License';
            
            container.innerHTML = `
                <div class="license-info">
                    <h3>License Details</h3>
                    
                    <div class="info-row">
                        <span class="info-label">License Key:</span>
                    </div>
                    <div class="license-key">${license.licenseKey}</div>
                    
                    <div class="info-row">
                        <span class="info-label">License Type:</span>
                        <span class="info-value">${license.licenseType.charAt(0).toUpperCase() + license.licenseType.slice(1)}</span>
                    </div>
                    
                    <div class="info-row">
                        <span class="info-label">Max Devices:</span>
                        <span class="info-value">${license.maxDevices}</span>
                    </div>
                    
                    <div class="info-row">
                        <span class="info-label">Email:</span>
                        <span class="info-value">${license.email}</span>
                    </div>
                    
                    <div class="info-row">
                        <span class="info-label">Status:</span>
                        <span class="info-value">${expirationText}</span>
                    </div>
                    
                    <div class="info-row">
                        <span class="info-label">Amount Paid:</span>
                        <span class="info-value">$${(session.amountTotal / 100).toFixed(2)} ${session.currency.toUpperCase()}</span>
                    </div>
                </div>
                
                <div style="margin-top: 20px; padding: 15px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; color: #155724;">
                    <strong>Important:</strong> Save your license key in a secure location. You'll need it to activate the application on your devices.
                    A copy has also been sent to your email address.
                </div>
            `;
            
            document.getElementById('copy-btn').style.display = 'inline-block';
        }

        function showError(message) {
            const container = document.getElementById('license-container');
            container.innerHTML = `
                <div class="error">
                    <strong>Error:</strong> ${message}
                </div>
            `;
        }

        function copyLicenseKey() {
            if (!currentLicenseKey) return;
            
            navigator.clipboard.writeText(currentLicenseKey).then(() => {
                const btn = document.getElementById('copy-btn');
                const originalText = btn.textContent;
                btn.textContent = 'Copied!';
                btn.style.background = '#28a745';
                
                setTimeout(() => {
                    btn.textContent = originalText;
                    btn.style.background = '#0066cc';
                }, 2000);
            }).catch(() => {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = currentLicenseKey;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                
                const btn = document.getElementById('copy-btn');
                const originalText = btn.textContent;
                btn.textContent = 'Copied!';
                btn.style.background = '#28a745';
                
                setTimeout(() => {
                    btn.textContent = originalText;
                    btn.style.background = '#0066cc';
                }, 2000);
            });
        }

        // Load license information when page loads
        loadLicenseInfo();
    </script>
</body>
</html>
